import { Router, Request } from "itty-router";
import { convertObjTo<PERSON>son } from "./help/convert";
import {
  responseError,
  responseSuccess,
  responseStatusError,
} from "./help/response";
import ags_restauth from "@agilesoft/type_ags_authrest2";
import cors from "cors";
import {
  Validate,
  ValidateDemoType,
  ValidateEndpoint,
  ValidateEndpointName,
  ValidateOnlyCttcode,
  ValidateOnlyPhoneFirebase,
  ValidateRegisterDataCust,
  ValidateSaveNoti,
  ValidateUpdateNoti,
  VaridateLoginUsername,
  ValidateOnlyIdCard,
  ValidateOnlyNotify,
  ValidateCheckLoanStatus,
  ValidateCheckCarBrandYear,
  ValidateCheckCarModelYear,
  ValidateCheckCarTypeCarbrand,
  ValidateGuaranteeModel,
  ValidateContractDetail,
  ValidateOfferMR,
  ValidateGetReferFriend,
  ValidateEditDataCust,
  ValidateSaveRequestLoan,
  ValidatePayLikeActivity,
  ValidateRegisMR,
  ValidateLonOnlineDropdown,
  ValidateGuaranteeFromType,
  ValidateLoanProvinceDropdown,
  ValidateLoanDistrictDropdown,
  ValidateLoanSubDistrictDropdown,
  ValidateOCRScan,
  ValidateUpdateAppAgreement,
  ValidateUpdateAddressDigital,
  ValidateGuaranteeSubTypeFromBrand,
  ValidateCustcode,
  ValidateBankName,
  ValidateGetBankData,
  ValidateOfferMRWithLP2,
  ValidateUpdateFCMToken,
  ValidateFeedback,
  ValidateRegisterDataCustWithLP2,
  ValidateSearchHistoryMR,
  ValidateSearchHistoryReferal,
  ValidateDeleteAllNoti,
  ValidateSearchData,
  ValidateQRPayments,
  ValidateUploadBillPayments,
  ValidateUId,
  ValidateUpdateLoginUser,
  ValidateCheckFeedback,
  ValidateFeedbackMultiType,
  ValidateMenuName,
  ValidateAcceptRejectedStatus,
  ValidateUpdateBookBank,
  ValidateUpdateRefCode,
  ValidateGetReferralDownload,
  ValidateSendNoti,
  ValidateDigitalContract,
  ValidateArCardPreview,
  ValidateLogDigitalLoan,
  ValidateMessageTGGroup,
  ValidateUpdateDataByField,
  ValidateSendNotiByTopic,
  ValidateCarModelFromYear,
  ValidateLoanOnlineRequest,
  ValidateSelectVillage,
  ValidateLogmenu,
} from "./validate/productValidate";
import {
  DemoType,
  Endpoint,
  EndpointName,
  LoginUsername,
  OnlyCttcode,
  OnlyPhoneFirebase,
  RegisterDataCust,
  SaveNoti,
  UpdateNoti,
  OnlyIdCard,
  OnlyNotify,
  OnlyCheckLoanStatus,
  OnlyCarBrandYear,
  OnlyCheckCarTypeFromCarbrand,
  OnlyCheckGuaModel,
  OnlySaveRequestLoan,
  EditDataCust,
  ContractDetail,
  OnlyOfferMR,
  OnlyGetReferFriend,
  OnlyPayLikeActivity,
  OnlyRegisMR,
  OnlyDropDownGeneral,
  OnlySelectProvince,
  OnlySelectDistrict,
  OnlySelectSubDistrict,
  OnlyOCR,
  AppAgreement,
  UpdateAddressDigital,
  Custcode,
  BankName,
  GetBankData,
  OnlyOfferMRWithLP2,
  OnlyUpdateFCMToken,
  Feedback,
  RegisterDataCustWithLP2,
  OnlySearchHistoryMR,
  OnlySearchHistoryReferal,
  OnlyDeleteAllNoti,
  OnlySearchData,
  OnlyQRPayments,
  OnlyUploadBillPayments,
  OnlyUId,
  OnlyUpdateLoginUser,
  OnlyCheckFeedback,
  OnlyFeedbackMultiType,
  OnlyMenuName,
  OnlyAcceptRejectedStatus,
  OnlyUpdateBookBank,
  OnlyUpdateRefCode,
  OnlyGetReferralDownload,
  SendNoti,
  CrecateDigitalContract,
  ArCardPreview,
  OnlyLogDigitalLoan,
  OnlyMessageTGGroup,
  UpdateDataByField,
  SendNotiByTopic,
  OnlyCarModelBrandYear,
  OnlyLoanOnlineForm,
  OnlySelectVillage,
  OnlylogMenu,
} from "./type/productType";
import { registerCust, registerCust_V2 } from "./module/data_customer/register";
import { loginUsername } from "./module/login/loginUsername";
import { getNews } from "./module/news&promotion.ts/getNews";
import { getNotification } from "./module/notification/getNotification";
import { saveNotification } from "./module/notification/saveNotification";
import { sendNotification, sendNotificationTopic } from "./module/notification/sendNotification";
import {
  updateStatusNoti,
  deleteStatusNotiAll,
} from "./module/notification/updateStatusReadAndDelete";
import { updateFCMToken } from "./module/notification/updateToken";
import { loginWithPhone } from "./module/login/loginWithPhone";
import {
  getDataCust,
  getCustCode,
} from "./module/data_customer/getDataCustommer";
import { getContractDigisafety } from "./module/contract/getContractDigisafety_NEW";
import { getlistContractDigisafety } from "./module/contract/getlistContractDigisafety";
import { getlistContractDigital } from "./module/contract/getlistContractDigi_new";
import {
  updateAddressDigital,
  checkUpdateAddressDigital,
  assumeBookBankData,
} from "./module/contract/updateAddressDigital";
import { getEndpoint } from "./module/endpoint/getEndpoint";
import { saveEndpoint } from "./module/endpoint/saveEndpoint";
import { getMRData } from "./module/mr/getMRData";
import {
  referFriend,
  referFriend_V2,
  saveReferMRto4no,
} from "./module/mr/referFriend";
import { mr_referFriend } from "./module/mr/referFriend_new";
import {
  getReferFriendData,
  getReferralDownload,
} from "./module/mr/getReferFriend";
import { registerMR } from "./module/mr/registerMR";
import {
  checkLoanStatus,
  acceptRejectedLoanStatus,checkLoanStatusAMS3
} from "./module/loan/checkLoanStatus";
import { saveRequestLoan } from "./module/loan/saveRequestLoan";
import { carBrandFromYear } from "./module/loan/dropdownCarBrandFromyear";
import { carModelFromYearBrand } from "./module/loan/dropdownCarModelFromYearBrand";
import { carTypeByCarbrand } from "./module/loan/dropdownCarTypeFromCarbrand";
import {
  updateDataCust,
  updateReferalCode,
} from "./module/data_customer/editDataCustommer";
import {
  disableAccount,
  checkDeleteAccount,
  recoverAccount,
  setAccountPendingDeletion,
  setAccountDeletion,
} from "./module/data_customer/accountSetting";
import {
  updateAppAgreement,
  updateAppPrivacyPolicy,
  updateAICPAgreement,
} from "./module/agreement/appAgreement";
import { updateDigitalAgreement } from "./module/agreement/aampayAgreement";
import { payLikeByActivity } from "./module/likepoint/payLikeByActivity";
import { getMRReferralHistory } from "./module/likepoint/getTransectionHistory";
import { getCareer } from "./module/component/getCareer";
import { dropdownLoanOnline } from "./module/loan/dropdownLoanOnline";
import {
  dropdownLoanOnlineProvince,
  dropdownLoanOnlineDistrict,
  dropdownLoanOnlineSubDistrict,
  dropdownLoanOnlineVillage,
} from "./module/loan/dropdownAddress";
import { guaranteeBrandFromType } from "./module/loan/dropdownGauranteeBrandFromType";
import { guaranteeTypeFromBrand } from "./module/loan/dropdownGuaTypeFromGuaBrand";
import { guaranteeModel } from "./module/loan/dropdownGuaModel";
import { ocrAPI } from "./module/ocr/ocrAPI";
import { getBookBankData } from "./module/component/getBookbank";
import { getAddress } from "./module/address/getAddress";
import {
  saveFeedback,
  saveDeleteFeedback,
  checkAlertFeedback,
  saveFeedbackMultiType,
  getFeedbackByMenuName,
} from "./module/feedback/feedback";
import {
  searchHistoryMR,
  searchHistoryReferal,
  searchReferalDownload,
  checkRefCode,
} from "./module/mr/getReferal";
import { getBranchData, searchBranchData } from "./module/branch/getBranchData";
import { getQRPayments } from "./module/billPayments/getQRPayments";
import { updateBillPayments } from "./module/billPayments/updateBillPayments";
import {
  checkFaceBookUsers,
  updateFaceBookUsers,
  loginWithFacebook,
} from "./module/social_login/facebookLogin";
import { updateBookbank } from "./module/kyc/bookbank";
import { createDigitalContract } from "./module/digitalContract/createDigitalContract";
import {
  getARCardPreview,
  checkDigitalLoanLimit,
  sendDigitalLog,
  sendTelegramGroup,
} from "./module/digitalContract/digitalContract";
import {
  updateCustDataByField
} from "./module/admin/updateDataCustomer";
import {
  saveLoanOnlineRequest
} from "./module/loan/saveLoanOnlineRequest";
import {
  diaryactiveusers
} from "./module/diaryactiveusers/dau";

// initial api running
const router = Router();

declare let global: GlobalEnvironment;

interface GlobalEnvironment {
  ENV_DEPLOY: string;
  MainDB: string;
}

/*
 อ่านก่อนร้อง
 
 ######  #######    #    ######     #     # #######    ### ### 
 #     # #         # #   #     #    ##   ## #          ### ### 
 #     # #        #   #  #     #    # # # # #          ### ### 
 ######  #####   #     # #     #    #  #  # #####       #   #  
 #   #   #       ####### #     #    #     # #                  
 #    #  #       #     # #     #    #     # #          ### ### 
 #     # ####### #     # ######     #     # #######    ### ### 


sub path ก่อนเข้า module จะทำ switcher ไว้ ในขั้นตอนก่อน init cors ชื่อว่า adjust other bu path here

สำหรับ UAT จะมีการ แทนค่า  buSwitchPath แล้วต่อด้วย UAT ถ้าจะ deploy product อย่าลืม comment บรรทัดนั้นทิ้งไป
*/

global.ENV_DEPLOY = ENVIRONMENT || "dev" || "";
global.MainDB = aamDB;
global.userAuth = "aamMobile";

console.log("ENV_DEPLOY");
console.log(ENV_DEPLOY);
if (ENV_DEPLOY == "production") {
  console.log("production");
  var Auth = new ags_restauth(R_TOKEN, R_USER);
}

// adjust other bu path here
var buSwitchPath = "AAMGp3";

if (BUNAME == "rplc") {
  buSwitchPath = "RPLCp3";
  global.MainDB = rplcDB;
  global.userAuth = "rplcMobile";
} else if (BUNAME == "rafco") {
  buSwitchPath = "RAFCOp3";
  global.MainDB = rafcoDB;
  global.userAuth = "rafcoMobile";
}

// buSwitchPath = buSwitchPath + "-UAT"; ///<----- ตรงนี้ถ้าต้องการให้เป็น UAT ถ้าต้องการให้เป็น Product ให้ commect บรรทัดนี้ไป

console.log(buSwitchPath);

// initail cors

const allowedOrigins = ["*"];

const options: cors.CorsOptions = {
  origin: allowedOrigins,
};

var corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET,HEAD,POST,PUT,OPTIONS",
  "Access-Control-Max-Age": "86400",
};

router.use(cors(options));

// end initial

const deCode = async (req: any) => {
  // for decyption
  var data;
  return new Promise((resolve, reject) => {
    (async () => {
      try {
        if (ENV_DEPLOY === "production") {
          // production-specific code
          console.log("production");
          data = await Auth.Middleware(req); /// ***** ใช้เมื่อปล่อยใช้จริง ( # สำคัญ # ปิดไว้เพื่อเทสข้างนอก ) ******
          // data = await req.json();
        } else {
          // staging-specific code
          console.log("staging");
          data = await req.json();
        }
        //   console.log(data);
        return resolve(data);
      } catch (error) {
        console.log(error);
        return reject(error);
      }
    })();
  });
};

// path function api
router.get("/" + buSwitchPath + "/test", async () => {
  const data = {
    version: "1.0",
    massage: "connected",
  };
  return await responseSuccess(data, 200);
});

//local deploy test

// for test local init
// router.post("/"+buSwitchPath+"/testParam", async (request: Request) => {
//     try {
//         const req = await deCode(request);
//         const datacheck = await Validate(req, ValidateDemoType);

//         const payload = (await convertObjToJson(datacheck)) as DemoType;
//         const data = await payload;

//         switch (data) {
//             case 0:
//                 return await responseError(data, 403);
//             case 1:
//                 return await responseError("can not get info from DB", 401);
//             case 2:
//                 return await responseError("function error", 402);
//             default:
//                 return await responseSuccess(data, 200);
//         }

//     } catch (error) {
//         console.log(error);
//         return await responseError(error, 400);
//     }
// });

/// Get URL API ENDPOINTMENT
router.post("/" + buSwitchPath + "/getEndpoint", async (request: Request) => {
  try {
    const req = await deCode(request);
    const datacheck = await Validate(req, ValidateEndpointName);

    const payload = (await convertObjToJson(datacheck)) as EndpointName;

    const data = await getEndpoint(payload);

    return await responseSuccess(data, 200);
  } catch (error) {
    console.log(error);
    return await responseError(error, 400);
  }
});

router.post("/" + buSwitchPath + "/saveEndpoint", async (request: Request) => {
  try {
    const req = await deCode(request);
    const datacheck = await Validate(req, ValidateEndpoint);

    const payload = (await convertObjToJson(datacheck)) as Endpoint;
    const data = await saveEndpoint(payload);

    return await responseSuccess(data, 200);
  } catch (error) {
    console.log(error);
    return await responseError(error, 400);
  }
});
///

router.get("/" + buSwitchPath + "/getNewsWithBU", async (request) => {
  const { searchParams } = new URL(request.url); // ดึง query parameters จาก URL
  const bu = searchParams.get("bu"); // ดึงค่าจาก query parameter 'param1'
  console.log(bu);
  // ใช้ค่าจาก query parameters ในการประมวลผล
  const data = await getNews();

  switch (data) {
    case 0:
      return await responseError(data, 403);
    default:
      return await responseSuccess(data, 200);
  }
});

router.get("/" + buSwitchPath + "/getNews", async (request: Request) => {
  const data = await getNews();
  switch (data) {
    case 0:
      return await responseError(data, 403);
    default:
      return await responseSuccess(data, 200);
  }
});

router.post("/" + buSwitchPath + "/getNews", async (request: Request) => {
  const data = await getNews();
  if (data != 0) {
    return await responseSuccess(data, 200);
  } else {
    return await responseError(data, 403);
  }
});

router.post("/" + buSwitchPath + "/register", async (request: Request) => {
  try {
    const req = await deCode(request);
    const datacheck = await Validate(req, ValidateRegisterDataCust);

    const payload = (await convertObjToJson(datacheck)) as RegisterDataCust;
    const data = await registerCust(payload);

    switch (data) {
      case 0:
        return await responseError(data, 403);
      case 1:
        return await responseError("can not get info from DB", 401);
      case 2:
        return await responseError("function error", 402);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(error);
    return await responseError(error, 400);
  }
});

router.post("/" + buSwitchPath + "/register_V2", async (request: Request) => {
  try {
    const req = await deCode(request);
    const datacheck = await Validate(req, ValidateRegisterDataCustWithLP2);

    const payload = (await convertObjToJson(
      datacheck
    )) as RegisterDataCustWithLP2;
    const data = await registerCust_V2(payload);

    switch (data) {
      case 0:
        return await responseError(data, 403);
      case 1:
        return await responseError("can not get info from DB", 401);
      case 2:
        return await responseError("function error", 402);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(error);
    return await responseError(error, 400);
  }
});

router.post("/" + buSwitchPath + "/loginUsername", async (request: Request) => {
  try {
    const req = await deCode(request);
    const datacheck = await Validate(req, VaridateLoginUsername);

    const payload = (await convertObjToJson(datacheck)) as LoginUsername;
    const data = await loginUsername(payload);

    switch (data) {
      case 0:
        return await responseError(data, 403);
      case 1:
        return await responseError("can not get info from DB", 401);
      case 2:
        return await responseError("function error", 402);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/loginWithPhone",
  async (request: Request) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);

      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await loginWithPhone(payload);

      switch (data) {
        case 0:
          return await responseError(data, 403);
        case 1:
          return await responseError("can not get info from DB", 401);
        case 2:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/saveNotification",
  async (request: Request) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateSaveNoti);

      const payload = (await convertObjToJson(datacheck)) as SaveNoti;
      const data = await saveNotification(payload);

      switch (data) {
        case 0:
          return await responseError(data, 403);
        case 1:
          return await responseError("can not get info from DB", 401);
        case 2:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getNotification",
  async (request: Request) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateOnlyNotify);

      const payload = (await convertObjToJson(datacheck)) as OnlyNotify;
      const data = await getNotification(payload);

      switch (data) {
        case 0:
          return await responseError("Data Not found", 404);
        case 1:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateStatusNotification",
  async (request: Request) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateUpdateNoti);

      const payload = (await convertObjToJson(datacheck)) as UpdateNoti;
      const data = await updateStatusNoti(payload);

      switch (data) {
        case 0:
          return await responseError(data, 403);
        case 1:
          return await responseError("can not get info from DB", 401);
        case 2:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/deleteAllNotification",
  async (request: Request) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateDeleteAllNoti);
      const payload = (await convertObjToJson(datacheck)) as OnlyDeleteAllNoti;
      const data = await deleteStatusNotiAll(payload);

      switch (data) {
        case 0:
          return await responseError(data, 403);
        case 1:
          return await responseError("can not get info from DB", 401);
        case 2:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateFCMToken",
  async (request: Request) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateUpdateFCMToken);

      const payload = (await convertObjToJson(datacheck)) as OnlyUpdateFCMToken;
      const data = await updateFCMToken(payload);

      switch (data) {
        case 0:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getDataCust",
  async (request: OnlyPhoneFirebase) => {
    try {
      const req = await deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);

      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await getDataCust(payload);

      switch (data) {
        case 0:
          return await responseError(data, 403);
        case 1:
          return await responseError("can not get info from DB", 401);
        case 2:
          return await responseError("function error", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/getCustCode", async (request: Request) => {
  try {
    const req = await deCode(request);
    const datacheck = await Validate(req, ValidateOnlyIdCard);

    const payload = (await convertObjToJson(datacheck)) as OnlyIdCard;
    const data = await getCustCode(payload);

    switch (data) {
      case 0:
        return await responseError(data, 403);
      case 1:
        return await responseError("can not get info from DB", 401);
      case 2:
        return await responseError("function error", 402);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/getContractDigisafety",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateContractDetail);
      const payload = (await convertObjToJson(datacheck)) as ContractDetail;
      const data = await getContractDigisafety(payload);
      switch (data) {
        case 0:
          return await responseError(data, 404);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getlistContractDigisafety",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateCheckLoanStatus);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyCheckLoanStatus;
      const data = await getlistContractDigital(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/checkUpdateAddressDigital",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateCustcode);
      const payload = (await convertObjToJson(datacheck)) as Custcode;
      const data = await checkUpdateAddressDigital(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/assumeBookBankData",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateBankName);
      const payload = (await convertObjToJson(datacheck)) as BankName;
      const data = await assumeBookBankData(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateAddressDigital",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUpdateAddressDigital);
      const payload = (await convertObjToJson(
        datacheck
      )) as UpdateAddressDigital;
      const data = await updateAddressDigital(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post("/" + buSwitchPath + "/getMRData", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
    const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
    const data = await getMRData(payload);

    switch (data) {
      case 0:
        return await responseError(data, 404);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 500);
  }
});

router.post(
  "/" + buSwitchPath + "/checkLoanStatus",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateCheckLoanStatus);
      const payload = (await convertObjToJson(datacheck)) as CheckLoanStatus;
      const data = await checkLoanStatus(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/checkLoanStatusAMS3",
  async (request: Request) => {
    try {
      const req = request.json(); //Not Auth
      const datacheck = await Validate(req, ValidateCheckLoanStatus);
      const payload = (await convertObjToJson(datacheck)) as CheckLoanStatus;
      const data = await checkLoanStatusAMS3(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/acceptRejectedLoanStatus",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateAcceptRejectedStatus);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyAcceptRejectedStatus;
      const data = await acceptRejectedLoanStatus(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/selectCarBrandFromYear",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateCheckCarBrandYear);
      const payload = (await convertObjToJson(datacheck)) as OnlyCarBrandYear;
      const data = await carBrandFromYear(payload);

      console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/selectGuaModel",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateGuaranteeModel);
      const payload = (await convertObjToJson(datacheck)) as OnlyCheckGuaModel;
      console.log(payload);
      const data = await guaranteeModel(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/selectCarTypeByCarbrand",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateCheckCarTypeCarbrand);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyCheckCarTypeFromCarbrand;
      const data = await carTypeByCarbrand(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/dropdownLoanOnline",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateLonOnlineDropdown);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyDropDownGeneral;
      const data = await dropdownLoanOnline(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

// router.get(
//   "/" + buSwitchPath + "/dropdownLoanOnlineProvince",
//   async (request) => {
//     const data = await dropdownLoanOnlineProvince();
//     console.log(data);
//     if (data) {
//       return await responseSuccess(data, 200);
//     } else {
//       console.log("dropdown");
//       return await responseError(data, 403);
//     }
//   }
// );

router.post(
  "/" + buSwitchPath + "/dropdownLoanOnlineProvince",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateLoanProvinceDropdown);
      const payload = (await convertObjToJson(datacheck)) as OnlySelectProvince;
      const data = await dropdownLoanOnlineProvince(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/dropdownLoanOnlineDistrict",
  async (request: Request) => {
    try {
      console.log("##### ");
      
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateLoanDistrictDropdown);
      const payload = (await convertObjToJson(datacheck)) as OnlySelectDistrict;
      const data = await dropdownLoanOnlineDistrict(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

/* for native */
router.post(
  "/" + buSwitchPath + "/UAT/dropdownLoanOnlineDistrict",
  async (request: Request) => {
    try {
      console.log("##### ");
      
      const req = request.json();
      const datacheck = await Validate(req, ValidateLoanDistrictDropdown);
      const payload = (await convertObjToJson(datacheck)) as OnlySelectDistrict;
      const data = await dropdownLoanOnlineDistrict(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/scanimage", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateOCRScan);
    const payload = (await convertObjToJson(datacheck)) as OnlyOCR;

    const data = await ocrAPI(payload);

    switch (data) {
      case 0:
        return await responseError(data, 500);
      case 1:
        return await responseError("not found", 404);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});


router.post(
  "/" + buSwitchPath + "/dropdownLoanOnlineSubDistrict",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateLoanSubDistrictDropdown);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySelectSubDistrict;
      const data = await dropdownLoanOnlineSubDistrict(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

/* for native */
router.post(
  "/" + buSwitchPath + "/UAT/dropdownLoanOnlineSubDistrict",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateLoanSubDistrictDropdown);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySelectSubDistrict;
      const data = await dropdownLoanOnlineSubDistrict(payload);

      // console.log(req);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/selectGuaranteeBrandFromType",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateGuaranteeFromType);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyCheckGuaranteeBrandFromType;
      const data = await guaranteeBrandFromType(payload);

      // console.log(req);
      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/selectGuaSubTypeFromBrand",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateGuaranteeSubTypeFromBrand);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySelectSubDistrict;
      const data = await guaranteeTypeFromBrand(payload);

      // console.log(req);
      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getReferFriendData",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateGetReferFriend);
      const payload = (await convertObjToJson(datacheck)) as OnlyGetReferFriend;
      const data = await getReferFriendData(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/saveRequestLoan",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateSaveRequestLoan);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySaveRequestLoan;
      const data = await saveRequestLoan(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.get("/" + buSwitchPath + "/getAddressWithBU", async (request) => {
  const { searchParams } = new URL(request.url); // ดึง query parameters จาก URL
  const bu = searchParams.get("bu"); // ดึงค่าจาก query parameter 'param1'
  console.log(bu);
  // ใช้ค่าจาก query parameters ในการประมวลผล
  const data = await getAddress();
  if (data) {
    return await responseSuccess(data, 200);
  } else {
    return await responseError(data, 403);
  }
});

router.post("/" + buSwitchPath + "/getAddress", async (request: Request) => {
  const data = await getAddress();
  if (data) {
    return await responseSuccess(data, 200);
  } else {
    return await responseError(data, 403);
  }
});

router.get("/" + buSwitchPath + "/getCareer", async () => {
  const data = await getCareer();
  if (data) {
    return await responseSuccess(data, 200);
  } else {
    return await responseError(data, 403);
  }
});

router.post(
  "/" + buSwitchPath + "/getBookBankData",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateGetBankData);
      const payload = (await convertObjToJson(datacheck)) as GetBankData;
      const data = await getBookBankData(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateDataCust",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateEditDataCust);
      const payload = (await convertObjToJson(datacheck)) as EditDataCust;
      const data = await updateDataCust(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateAppAgreement",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await updateAppAgreement(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateDigitalAgreement",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUpdateAppAgreement);
      const payload = (await convertObjToJson(datacheck)) as AppAgreement;
      const data = await updateDigitalAgreement(payload);

      switch (data) {
        case 0:
          return await responseError("error ", 402);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateAppPrivacyPolicy",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await updateAppPrivacyPolicy(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateAICPAgreement",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await updateAICPAgreement(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/referFriend", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateOfferMR);
    const payload = (await convertObjToJson(datacheck)) as OnlyOfferMR;
    const data = await referFriend(payload);

    switch (data) {
      case 0:
        return await responseError(data, 500);
      case 1:
        return await responseError("not found", 404);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/referFriend_V2",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOfferMRWithLP2);
      const payload = (await convertObjToJson(datacheck)) as OnlyOfferMRWithLP2;
      const data = await referFriend_V2(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/saveReferMRto4no",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOfferMR);
      const payload = (await convertObjToJson(datacheck)) as OnlyOfferMR;
      const data = await saveReferMRto4no(payload);

      switch (data.statusCode) {
        case 400:
          return await responseError(data.msg, 400);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/registerMR", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateRegisMR);
    const payload = (await convertObjToJson(datacheck)) as OnlyRegisMR;
    const data = await registerMR(payload);

    switch (data) {
      case 0:
        return await responseError(data, 500);
      case 1:
        return await responseError("not found", 404);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/payLikeByActivity",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidatePayLikeActivity);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyPayLikeActivity;
      const data = await payLikeByActivity(payload);

      switch (data) {
        case 0:
          return await responseError(data, 500);
        case 1:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getMRReferralHistory",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await getMRReferralHistory(payload);

      switch (data) {
        case 0:
          return await responseError("not found", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 500);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/disableAccount",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await disableAccount(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/checkDeleteAccount",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await checkDeleteAccount(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/recoverAccount",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await recoverAccount(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/saveFeedback", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateFeedback);
    const payload = (await convertObjToJson(datacheck)) as Feedback;
    const data = await saveFeedback(payload);

    switch (data.statusCode) {
      case 404:
        return await responseError(data.msg, data.statusCode);
      default:
        return await responseSuccess(data, data.statusCode);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/saveDeleteFeedback",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateFeedback);
      const payload = (await convertObjToJson(datacheck)) as Feedback;
      const data = await saveDeleteFeedback(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/searchHistoryMR",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateSearchHistoryMR);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySearchHistoryMR;
      const data = await searchHistoryMR(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data.result, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/searchHistoryReferal",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateSearchHistoryReferal);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySearchHistoryReferal;
      const data = await searchHistoryReferal(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data.result, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/searchReferalDownload",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateSearchHistoryReferal);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlySearchHistoryReferal;
      const data = await searchReferalDownload(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data.msg, data.statusCode);
        default:
          return await responseSuccess(data.result, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/getBranchData", async (request: Request) => {
  const req =await deCode(request);
  const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
  const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
  const data = await getBranchData(payload);
  if (data != 0) {
    return await responseSuccess(data, 200);
  } else {
    return await responseError(data, 403);
  }
});

router.post(
  "/" + buSwitchPath + "/searchBranchData",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateSearchData);
      const payload = (await convertObjToJson(datacheck)) as OnlySearchData;
      const data = await searchBranchData(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data, 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/getQRPayments", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateQRPayments);
    const payload = (await convertObjToJson(datacheck)) as OnlyQRPayments;
    const data = await getQRPayments(payload);

    switch (data.statusCode) {
      case 404:
        return await responseError(data, 404);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/updateBillPayments",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUploadBillPayments);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyUploadBillPayments;
      const data = await updateBillPayments(payload);

      switch (data.statusCode) {
        case 404:
          return await responseError(data, 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

//** Social Login */
router.post(
  "/" + buSwitchPath + "/checkFaceBookUsers",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUId);
      const payload = (await convertObjToJson(datacheck)) as OnlyUId;
      const data = await checkFaceBookUsers(payload);

      switch (data) {
        case 0:
          return await responseError(data, 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateFaceBookUsers",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUpdateLoginUser);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyUpdateLoginUser;
      const data = await updateFaceBookUsers(payload);

      switch (data) {
        case 0:
          return await responseError(data, 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/loginWithFacebook",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUId);
      const payload = (await convertObjToJson(datacheck)) as OnlyUId;
      const data = await loginWithFacebook(payload);

      switch (data) {
        case 0:
          return await responseError("Facebook login failed", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/checkAlertFeedback",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateCheckFeedback);
      const payload = (await convertObjToJson(datacheck)) as OnlyCheckFeedback;
      const data = await checkAlertFeedback(payload);

      switch (data) {
        case 0:
          return await responseError("check feedback by menu failed", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/saveFeedbackMultiType",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateFeedbackMultiType);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyFeedbackMultiType;
      const data = await saveFeedbackMultiType(payload);

      switch (data) {
        case 0:
          return await responseError(
            "Error : Function Insert feedback data",
            404
          );
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getFeedbackByMenuName",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateMenuName);
      const payload = (await convertObjToJson(datacheck)) as OnlyMenuName;
      const data = await getFeedbackByMenuName(payload);

      switch (data) {
        case 0:
          return await responseError("Error : Not found feedback data", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

/*################     KYC    ##################*/
router.post(
  "/" + buSwitchPath + "/updateBookbank",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUpdateBookBank);
      const payload = (await convertObjToJson(datacheck)) as OnlyUpdateBookBank;
      const data = await updateBookbank(payload);

      switch (data) {
        case 0:
          return await responseError("Error : Not found Book Bank data", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/updateReferalCode",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateUpdateRefCode);
      const payload = (await convertObjToJson(datacheck)) as OnlyUpdateRefCode;
      const data = await updateReferalCode(payload);

      switch (data) {
        case 0:
          return await responseError(
            "Error : update ref code data failed",
            404
          );
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getReferralDownload",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateGetReferralDownload);
      const payload = (await convertObjToJson(
        datacheck
      )) as OnlyGetReferralDownload;
      const data = await getReferralDownload(payload);

      switch (data) {
        case 0:
          return await responseError("Error : Not found data", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post("/" + buSwitchPath + "/checkRefCode", async (request: Request) => {
  try {
    const req = deCode(request);
    const datacheck = await Validate(req, ValidateUpdateRefCode);
    const payload = (await convertObjToJson(datacheck)) as OnlyUpdateRefCode;
    const data = await checkRefCode(payload);

    switch (data) {
      case 0:
        return await responseError("Error : Not found data", 404);
      default:
        return await responseSuccess(data, 200);
    }
  } catch (error) {
    console.log(`error route`);
    return await responseError(error, 404);
  }
});

router.post(
  "/" + buSwitchPath + "/sendNotification",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateSendNoti);
      const payload = (await convertObjToJson(datacheck)) as SendNoti;
      const data = await sendNotification(payload);

      switch (data) {
        case 0:
          return await responseError("Error : Send noti failed", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/sendNotificationTopic",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateSendNotiByTopic);
      const payload = (await convertObjToJson(datacheck)) as SendNotiByTopic;
      const data = await sendNotificationTopic(payload);

      switch (data) {
        case 0:
          return await responseError("Error : Send noti failed", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/createDigitalContract",
  async (request: Request) => {
    try {
      // const req = deCode(request);
      const req = request.json(); //Not Auth
      const datacheck = await Validate(req, ValidateDigitalContract);

      if (datacheck == 0) {
        return await responseStatusError("Invalid request data", false, 400);
      }
      const payload = (await convertObjToJson(
        datacheck
      )) as CrecateDigitalContract;
      const data = await createDigitalContract(payload);

      if (typeof data.status !== "boolean") {
        return await responseStatusError(
          "Invalid status type",
          false,
          data?.statusCode || 500
        );
      }

      switch (data.status) {
        case true:
          const res = {
            statusCode: 200,
            status: true,
            ctt_code: data.response.ctt_code,
            msg: data.response.msg,
            data: data.response.data,
          };
          return new Response(JSON.stringify(res), {
            statusCode: res.statusCode,
            headers: {
              "Content-Type": "application/json; charset=utf-8",
              ...corsHeaders,
            },
          });
        case false:
          return await responseStatusError(data.error, false, data.statusCode);
        default:
          return await responseStatusError(data.error, false, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseStatusError(data.error, false, data.statusCode);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/getARCardPreview",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateArCardPreview);
      if (datacheck == 0) {
        return await responseStatusError("Invalid request data", false, 400);
      }
      const payload = (await convertObjToJson(datacheck)) as ArCardPreview;
      const data = await getARCardPreview(payload);

      console.log(data);

      if (typeof data.status !== "boolean") {
        return await responseStatusError(
          "Invalid status type",
          false,
          data?.statusCode || 500
        );
      }

      switch (data.status) {
        case true:
          const res = {
            statusCode: 200,
            status: true,
            msg: data.response.msg,
            result: data.response.data,
          };
          return new Response(JSON.stringify(res), {
            statusCode: res.statusCode,
            headers: {
              "Content-Type": "application/json; charset=utf-8",
              ...corsHeaders,
            },
          });
        case false:
          return await responseStatusError(data.error, false, data.statusCode);
        default:
          return await responseStatusError(data.error, false, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseStatusError(data.error, false, data.statusCode);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/checkDigitalLoanLimit",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateCustcode);
      if (datacheck == 0) {
        return await responseStatusError("Invalid request data", false, 400);
      }
      const payload = (await convertObjToJson(datacheck)) as Custcode;
      const data = await checkDigitalLoanLimit(payload);

      console.log(data);

      if (typeof data.status !== "boolean") {
        return await responseStatusError(
          "Invalid status type",
          false,
          data?.statusCode || 500
        );
      }

      switch (data.status) {
        case true:
          const res = {
            statusCode: 200,
            status: 200,
            result: data.response.data,
            guarantee_id: data.response.guarantee_id,
            current_contract: data.response.current_contract,
            money_approve: data.response.money_approve,
            debt_loan: data.response.debt_loan,
            remain_loan: data.response.remain_loan,
          };
          return new Response(JSON.stringify(res), {
            statusCode: res.statusCode,
            headers: {
              "Content-Type": "application/json; charset=utf-8",
              ...corsHeaders,
            },
          });
        case false:
          return await responseStatusError(data.error, false, data.statusCode);
        default:
          return await responseStatusError(data.error, false, data.statusCode);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseStatusError(data.error, false, data.statusCode);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/sendDigitalLog",
  async (request: Request) => {
    try {
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateLogDigitalLoan);
      const payload = (await convertObjToJson(datacheck)) as OnlyLogDigitalLoan;
      const data = await sendDigitalLog(payload);

      switch (data) {
        case 0:
          return await responseError("error : can't send notification", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/sendTelegramGroup",
  async (request: Request) => {
    try {
      const req = request.json(); //Not Auth
      const datacheck = await Validate(req, ValidateMessageTGGroup);
      const payload = (await convertObjToJson(datacheck)) as OnlyMessageTGGroup;
      const data = await sendTelegramGroup(payload);

      switch (data) {
        case 0:
          return await responseError("error : can't send notification", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/carModelFromYearBrand",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateCarModelFromYear);
      const payload = (await convertObjToJson(datacheck)) as OnlyCarModelBrandYear;
      const data = await carModelFromYearBrand(payload);

      switch (data) {
        case 0:
          return await responseError("error", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);


router.post(
  "/" + buSwitchPath + "/saveLoanOnlineRequest",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateLoanOnlineRequest);
      const payload = (await convertObjToJson(datacheck)) as OnlyLoanOnlineForm;
      const data = await saveLoanOnlineRequest(payload);

      switch (data) {
        case 0:
          return await responseError("error", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/dropdownLoanOnlineVillage",
  async (request: Request) => {
    try {
      const req = request.json();
      const datacheck = await Validate(req, ValidateSelectVillage);
      const payload = (await convertObjToJson(datacheck)) as OnlySelectVillage;
      const data = await dropdownLoanOnlineVillage(payload);

      switch (data) {
        case 0:
          return await responseError("error", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/saveActiveUsers",
  async (request: Request) => {
    try {
      
      const req = deCode(request);
      const datacheck = await Validate(req, ValidateLogmenu);
      const payload = (await convertObjToJson(datacheck)) as OnlylogMenu;
      const data = await diaryactiveusers(payload);

      switch (data) {
        case 0:
          return await responseError("error", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

/*
##################################################
#                                                #
#                !!! ADMIN !!!                   #
#                                                #
#           !!! ฟังก์ชันสำหรับ ADMIN !!!             #
#                                                #
#                                                #
##################################################
*/
router.post(
  "/" + buSwitchPath + "/admin/updateCustDataByField",
  async (request: Request) => {
    try {
      const req = request.json(); //Not Auth
      const datacheck = await Validate(req, ValidateUpdateDataByField);
      const payload = (await convertObjToJson(datacheck)) as UpdateDataByField;
      const data = await updateCustDataByField(payload);

      switch (data) {
        case 0:
          return await responseError("error : update faild", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/admin/getUserByPhone",
  async (request: Request) => {
    try {
      const req = request.json(); //Not Auth
      const datacheck = await Validate(req, ValidateOnlyPhoneFirebase);
      const payload = (await convertObjToJson(datacheck)) as OnlyPhoneFirebase;
      const data = await getDataCust(payload);

      switch (data) {
        case 0:
          return await responseError("error : not found user data", 404);
        default:
          return await responseSuccess(data, 200);
      }
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

/*
##################################################
#                                                #
#                !!! CAUTION !!!                 #
#                                                #
#  !!! cron job function สำหรับ เมนู ลบบัญชี !!!     #
#                                                #
#                                                #
##################################################
*/

router.post(
  "/" + buSwitchPath + "/setAccountPendingDeletion",
  async (request: Request) => {
    try {
      const data = await setAccountPendingDeletion();
      return await responseSuccess(data, data.statusCode);
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

router.post(
  "/" + buSwitchPath + "/setAccountDeletion",
  async (request: Request) => {
    try {
      const data = await setAccountDeletion();
      return await responseSuccess(data, data.statusCode);
    } catch (error) {
      console.log(`error route`);
      return await responseError(error, 404);
    }
  }
);

// function //
function handleOptions(request) {
  // Make sure the necessary headers are present
  // for this to be a valid pre-flight request
  let headers = request.headers;
  if (
    headers.get("Origin") !== null &&
    headers.get("Access-Control-Request-Method") !== null &&
    headers.get("Access-Control-Request-Headers") !== null
  ) {
    // Handle CORS pre-flight request.
    // If you want to check or reject the requested method + headers
    // you can do that here.
    let respHeaders = {
      ...corsHeaders,
      // Allow all future content Request headers to go back to browser
      // such as Authorization (Bearer) or X-Client-Name-Version
      "Access-Control-Allow-Headers": request.headers.get(
        "Access-Control-Request-Headers"
      ),
    };

    return new Response(null, {
      headers: respHeaders,
    });
  } else {
    // Handle standard OPTIONS request.
    // If you want to allow other HTTP Methods, you can do that here.
    return new Response(null, {
      headers: {
        Allow: "GET, HEAD, POST, OPTIONS",
      },
    });
  }
}

router.handle({ method: "GET", url: "https://example.com/pass/user" });
// withUser injects user, allowing requireUser to not return/continue

// attach the router "handle" to the event handler
async function handleRequestWithTimeout(request: Request) {
  // Maximum time limit for the task
  const TIMEOUT_LIMIT = 120000; // 120000 milliseconds or 2 min

  // Create a timeout promise
  const timeoutPromise = new Promise(
    (resolve) =>
      setTimeout(
        () => resolve(new Response("Request timed out", { status: 504 })),
        TIMEOUT_LIMIT
      ) // 504 Gateway Timeout
  );

  // Main task: Using router.handle(request) or your other response logic
  const mainTask = new Promise(async (resolve) => {
    try {
      const response = await router.handle(request);
      resolve(response);
    } catch (error) {
      resolve(new Response("Error processing request", { status: 500 }));
    }
  });

  // Use Promise.race to race between main task and timeout
  return Promise.race([mainTask, timeoutPromise]);
}

addEventListener("fetch", (event) => {
  const request = event.request;
  const url = new URL(request.url);

  if (request.method === "OPTIONS") {
    // Handle CORS preflight requests
    event.respondWith(handleOptions(request));
  } else if (
    request.method === "GET" ||
    request.method === "HEAD" ||
    request.method === "PUT" ||
    request.method === "DELETE" ||
    request.method === "POST"
  ) {
    // Handle requests to the API server with timeout
    event.respondWith(handleRequestWithTimeout(request)); //set time out
  } else {
    event.respondWith(
      new Response(null, {
        status: 405,
        statusText: "Method Not Allowed",
      })
    );
  }
});

//------------------------------- cron job on Listener time ---------------------------------
addEventListener("scheduled", (event) => {
  // console.log("event", event);
  event.waitUntil(startcron(event.scheduledTime));
});

//------------------------------- call function cron job ---------------------------------
async function startcron() {
  await setAccountPendingDeletion();

  // หน่วงเวลา 1 นาที (60,000 มิลลิวินาที)
  await new Promise((resolve) => setTimeout(resolve, 60000));

  await setAccountDeletion();
}