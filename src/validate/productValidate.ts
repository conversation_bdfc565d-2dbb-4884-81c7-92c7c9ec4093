import Joi from "joi";
import { responseError } from "../help/response";

export const Validate = async (payload: any, schema) => {
  try {
    const params = await payload;
    const { error } = await schema.validate(params);
    if (error) {
      console.log(error);
      console.log("log null");

      return 0;
    } else {
      payload = params;
      return params;
    }
  } catch (e) {
    console.log(e);
    return await responseError("Wrong Parameter", 407);
  }
};

export const ValidateDemoType = Joi.object({
  message: Joi.string().required(),
});

export const ValidateEndpoint = Joi.object({
  endpoint: Joi.any().required(),
});

export const ValidateEndpointName = Joi.object({
  bu: Joi.string().required(),
});

export const ValidateRegisterDataCust = Joi.object({
  phone: Joi.string().required(),
  phoneFirebase: Joi.string().required(),
  displayName: Joi.any().allow("", null),
  typeConnect: Joi.string().required(),
  username: Joi.any().allow("", null),
  password: Joi.any().allow("", null),
  like_uid: Joi.any().allow("", null),
  firstname: Joi.string().required(),
  lastname: Joi.any().required(),
  refcode: Joi.any().allow("", null),
  birthday: Joi.any().allow("", null),
  email: Joi.any().allow("", null),
  idcard: Joi.any().allow("", null),
  tumbol: Joi.any().allow("", null),
  amphur: Joi.any().required(),
  province: Joi.any().required(),
  addressOther: Joi.any().allow("", null),
  zipcode: Joi.any().allow("", null),
  branch: Joi.any().allow("", null),
});

export const ValidateRegisterDataCustWithLP2 = Joi.object({
  phone: Joi.string().required(),
  phoneFirebase: Joi.string().required(),
  displayName: Joi.any().allow("", null),
  typeConnect: Joi.string().required(),
  username: Joi.any().allow("", null),
  password: Joi.any().allow("", null),
  like_uid: Joi.any().allow("", null),
  firstname: Joi.string().required(),
  lastname: Joi.any().required(),
  refcode: Joi.any().allow("", null),
  birthday: Joi.any().allow("", null),
  email: Joi.any().allow("", null),
  idcard: Joi.any().allow("", null),
  tumbol: Joi.any().allow("", null),
  amphur: Joi.any().required(),
  province: Joi.any().required(),
  addressOther: Joi.any().allow("", null),
  zipcode: Joi.any().allow("", null),
  branch: Joi.any().allow("", null),
  merchantID: Joi.string().allow("", null),
  activityID: Joi.string().allow("", null),
});

export const VaridateLoginUsername = Joi.object({
  username: Joi.string().required(),
  password: Joi.string().required(),
});

export const ValidateOnlyPhoneFirebase = Joi.object({
  phone_firebase: Joi.string().required(),
});

export const ValidateSaveNoti = Joi.object({
  phone_firebase: Joi.string().required(),
  title: Joi.string().required(),
  body: Joi.string().required(),
  type: Joi.any().allow("", null),
});

export const ValidateUpdateNoti = Joi.object({
  status: Joi.string().required(),
  running: Joi.any().required(),
  user_running: Joi.any().allow("", null),
});

export const ValidateFeedback = Joi.object({
  point: Joi.any(),
  message: Joi.any(),
  type: Joi.any(),
  image: Joi.any(),
  os: Joi.any(),
  fullname: Joi.any(),
  phone: Joi.any(),
  phone_firebase: Joi.any(),
});

export const ValidateEditDataCust = Joi.object({
  phone_firebase: Joi.any().required(),
  type: Joi.any().required(),
  data: Joi.any().required(),
});

export const ValidateOnlyCttcode = Joi.object({
  ctt_code: Joi.string().required(),
});

export const ValidateOnlyIdCard = Joi.object({
  idCard: Joi.string().required(),
});

export const ValidateOnlyNotify = Joi.object({
  user_running: Joi.string().required(),
  phone_firebase: Joi.string().required(),
});

export const ValidateCheckLoanStatus = Joi.object({
  phone: Joi.string().required(),
  idcard: Joi.string().required(),
});

export const ValidateContractDetail = Joi.object({
  ctt_code: Joi.string().required(),
  phone: Joi.any().allow("", null),
});

export const ValidateSaveRequestLoan = Joi.object({
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  phone: Joi.string().required(),
  phone_firebase: Joi.string().required(),
  line: Joi.string().allow("", null),
  facebook: Joi.string().allow("", null),
  idcard: Joi.string().allow("", null),
  guarantee_type: Joi.string().required(),
  request_loan: Joi.any().required(),
  request_installment: Joi.any().required(),
  province: Joi.string().required(),
  district: Joi.string().required(),
  branch: Joi.string().allow("", null),
  image: Joi.any().allow("", null),
});

export const ValidateOfferMR = Joi.object({
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  phone_customer: Joi.string().required(),
  province: Joi.string().required(),
  district: Joi.string().required(),
  guarantee: Joi.string().required(),
  imgGuarantee: Joi.any().allow("", null),
  mr_id: Joi.string().required(),
  mr_name: Joi.string().required(),
  mr_phone: Joi.string().required(),
  mr_rank: Joi.string().required(),
});

export const ValidateOfferMRWithLP2 = Joi.object({
  running_4no: Joi.any().required(),
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  phone_customer: Joi.string().required(),
  district: Joi.string().required(),
  province: Joi.string().required(),
  branch: Joi.string().required(),
  guarantee: Joi.string().required(),
  img1: Joi.any().allow("", null),
  img2: Joi.any().allow("", null),
  img3: Joi.any().allow("", null),
  mr_id: Joi.string().required(),
  mr_name: Joi.string().required(),
  mr_phone: Joi.string().required(),
  mr_rank: Joi.string().allow("", null),
  merchantID: Joi.string().required(),
  activityID: Joi.string().required(),
});

export const ValidateOfferMRWithLP2All = Joi.object({
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  phone_customer: Joi.string().required(),
  district: Joi.string().required(),
  province: Joi.string().required(),
  guarantee: Joi.string().required(),
  img1: Joi.any().allow("", null),
  img2: Joi.any().allow("", null),
  img3: Joi.any().allow("", null),
  mr_id: Joi.string().required(),
  mr_name: Joi.string().required(),
  mr_phone: Joi.string().required(),
  mr_rank: Joi.string().allow("", null),
  merchantID: Joi.string().required(),
  activityID: Joi.string().allow("", null),
});

export const ValidateGetReferFriend = Joi.object({
  mr_id: Joi.string().required(),
  phone_firebase: Joi.string().required(),
});

export const ValidateCheckCarBrandYear = Joi.object({
  carp_id: Joi.string().required(),
});

export const ValidateCheckCarModelYear = Joi.object({
  year: Joi.string(),
  carp_id: Joi.string(),
  carb_name_en: Joi.string().required(),
});

export const ValidateCheckCarTypeCarbrand = Joi.object({
  carb_id: Joi.string().required(),
  carp_id: Joi.string().required(),
});

export const ValidatePayLikeActivity = Joi.object({
  activityID: Joi.string().required(),
  phone_firebase: Joi.string().required(),
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  merchantID: Joi.string().required(),
});

export const ValidateRegisMR = Joi.object({
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  phone_firebase: Joi.string().required(),
  idcard: Joi.string().allow("", null),
  career: Joi.string().allow("", null),
  province: Joi.string().required(),
  district: Joi.string().allow("", null),
  subdistrict: Joi.string().allow("", null),
  zipcode: Joi.string().allow("", null),
});

export const ValidateLoanOnlineRequest = Joi.object({
  check_grant: Joi.string().allow("", null),
  idcard: Joi.string().required(),
  cust_id: Joi.string().allow("", null),
  prename: Joi.string().required(),
  cust_typeold: Joi.string().required(),
  cust_type: Joi.string().required(),
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  nickname: Joi.string().allow("", null),
  phone: Joi.string().required(),
  phone_firebase: Joi.string().required(),
  children: Joi.string(),
  age: Joi.string().allow("", null),
  grade: Joi.string().allow("", null),
  sex: Joi.string().required(),
  gender: Joi.string(),
  status: Joi.string(),
  cust_relationship: Joi.string().allow("", null),
  mate_name: Joi.string().allow("", null),
  mate_mobile: Joi.string().allow("", null),
  mate_career: Joi.string().allow("", null),
  mate_income: Joi.string().allow("", null),
  mate_office: Joi.string().allow("", null),
  mate_position: Joi.string().allow("", null),
  mate_job_live: Joi.string().allow("", null),
  residence_status: Joi.string().allow("", null),
  residence_live: Joi.string().allow("", null),
  occupation: Joi.string(),
  occupation_live: Joi.string(),
  occupation_trade_type: Joi.string().allow("", null),
  job: Joi.string().allow("", null),
  job_name: Joi.string(),
  office_name: Joi.string(),
  office_type: Joi.string().allow("", null),
  office_number: Joi.string(),
  office_room: Joi.string().allow("", null),
  office_village: Joi.string().allow("", null),
  office_moo: Joi.string(),
  office_soi: Joi.string().allow("", null),
  office_road: Joi.string().allow("", null),
  office_province: Joi.string(),
  office_city: Joi.string(),
  office_district: Joi.string(),
  office_postcode: Joi.string(),
  office_mobile1: Joi.string().allow("", null),
  office_mobile2: Joi.string().allow("", null),
  office_mobile_expand: Joi.string().allow("", null),
  office_fax: Joi.string().allow("", null),
  office_depart: Joi.string().allow("", null),
  office_position: Joi.string().allow("", null),
  office_startdate: Joi.string().allow("", null),
  office_live: Joi.string().allow("", null),
  old_office_name: Joi.string().allow("", null),
  old_office_mobile: Joi.string().allow("", null),
  old_office_position: Joi.string().allow("", null),
  old_office_live: Joi.string().allow("", null),
  old_guarantee: Joi.string().allow("", null),
  old_money_approve: Joi.string().allow("", null),
  old_installment: Joi.string().allow("", null),
  person_type: Joi.string(),
  income_month_expand: Joi.string().allow("", null),
  income_bonus: Joi.string(),
  income_type: Joi.string(),
  income_month_every: Joi.string(),
  income_day_expand: Joi.string(),
  income_average_expand: Joi.string().allow("", null),
  income_other_expand: Joi.string().allow("", null),
  income_other_source: Joi.string().allow("", null),
  income_business: Joi.string(),
  income_business_net: Joi.string(),
  cost_per_month: Joi.string(),
  cost_consumption: Joi.string(),
  cost_home: Joi.string(),
  cost_other: Joi.string(),
  debt_carloan: Joi.string(),
  debt_other: Joi.string(),
  debt_homeloan: Joi.string(),
  debt_invest: Joi.string(),
  birthday: Joi.string(),
  issue_date: Joi.string().allow("", null),
  card_expire: Joi.string().allow("", null),
  send_document: Joi.string(),
  contact1_name: Joi.string().allow("", null),
  contact1_nickname: Joi.string().allow("", null),
  contact1_relationship: Joi.string().allow("", null),
  contact1_position: Joi.string().allow("", null),
  contact1_depart: Joi.string().allow("", null),
  contact1_timeyear: Joi.string().allow("", null),
  contact1_mobile: Joi.string().allow("", null),
  contact1_office: Joi.string().allow("", null),
  contact1_officemore: Joi.string().allow("", null),
  contact1_lineid: Joi.string().allow("", null),
  contact1_email: Joi.string().allow("", null),
  contact1_facebook: Joi.string().allow("", null),
  contact2_name: Joi.string().allow("", null),
  contact2_nickname: Joi.string().allow("", null),
  contact2_relationship: Joi.string().allow("", null),
  contact2_number: Joi.string().allow("", null),
  contact2_room: Joi.string().allow("", null),
  contact2_village: Joi.string().allow("", null),
  contact2_moo: Joi.string().allow("", null),
  contact2_soi: Joi.string().allow("", null),
  contact2_road: Joi.string().allow("", null),
  contact2_province: Joi.string().allow("", null),
  contact2_postcode: Joi.string().allow("", null),
  contact2_mobile: Joi.string().allow("", null),
  contact2_office: Joi.string().allow("", null),
  contact2_lineid: Joi.string().allow("", null),
  contact2_email: Joi.string().allow("", null),
  cust_tel: Joi.string(),
  lineid: Joi.string().allow("", null),
  facebook: Joi.string().allow("", null),
  email: Joi.string().allow("", null),
  regist_number: Joi.string().allow("", null),
  regist_room: Joi.string().allow("", null),
  regist_village: Joi.string().allow("", null),
  regist_moo: Joi.string().allow("", null),
  regist_soi: Joi.string().allow("", null),
  regist_road: Joi.string().allow("", null),
  regist_district: Joi.string(),
  regist_city: Joi.string(),
  regist_province: Joi.string(),
  regist_postcode: Joi.string(),
  regist_mobile2: Joi.string().allow("", null),
  regist_live: Joi.string().allow("", null),
  regist_status: Joi.string().allow("", null),
  regist_nearby: Joi.string().allow("", null),
  current_address_type: Joi.string().allow("", null),
  current_number: Joi.string().allow("", null),
  current_room: Joi.string().allow("", null),
  current_village: Joi.string().allow("", null),
  regist_style: Joi.string(),
  current_moo: Joi.string().allow("", null),
  current_soi: Joi.string().allow("", null),
  current_road: Joi.string().allow("", null),
  current_district: Joi.string(),
  current_city: Joi.string(),
  current_province: Joi.string(),
  current_postcode: Joi.string(),
  current_mobile1: Joi.string().allow("", null),
  current_mobile2: Joi.string().allow("", null),
  current_live: Joi.string(),
  current_nearby: Joi.string().allow("", null),
  residence_style: Joi.string(),
  edit_request: Joi.string(),
  guarantee_type: Joi.string(),
  car_id: Joi.string().allow("", null),
  car_strid: Joi.string().allow("", null),
  car_reg: Joi.string().allow("", null),
  car_reg_province: Joi.string().allow("", null),
  crt_id: Joi.string().allow("", null),
  crt_num: Joi.string().allow("", null),
  car_year: Joi.string().allow("", null),
  car_brand: Joi.string().allow("", null),
  car_national: Joi.string().allow("", null),
  cart_id: Joi.string().allow("", null),
  car_model: Joi.string().allow("", null),
  engine_brand: Joi.string().allow("", null),
  fuel: Joi.string().allow("", null),
  car_engine: Joi.string().allow("", null),
  car_reg_date: Joi.string().allow("", null),
  car_color: Joi.string().allow("", null),
  car_date_possession: Joi.string().allow("", null),
  car_date_ownership: Joi.string().allow("", null),
  car_current_ownership: Joi.string().allow("", null),
  bl_codeG: Joi.string().allow("", null),
  bl_approveG: Joi.string().allow("", null),
  gc_type: Joi.string(),
  target_loan: Joi.string(),
  target_loan_expand: Joi.string().allow("", null),
  money_request: Joi.string(),
  bt_code: Joi.string(),
  installment_per: Joi.string().allow("", null),
  perloan_amount: Joi.string().allow("", null),
  loan_apply: Joi.string().allow("", null),
  mem_dispri: Joi.string().allow("", null),
  mem_dpay: Joi.string().allow("", null),
  mem_reqloan: Joi.string().allow("", null),
  mem_repay: Joi.string().allow("", null),
  mem_feeloan: Joi.string().allow("", null),
  periods: Joi.string(),
  mem_intrate: Joi.string().allow("", null),
  start_date: Joi.string().allow("", null),
  mem_crefee: Joi.string().allow("", null),
  end_date: Joi.string().allow("", null),
  mem_income: Joi.string().allow("", null),
  bor_acc: Joi.string().allow("", null),
  bor_bank: Joi.string().allow("", null),
  bor_branch: Joi.string().allow("", null),
  bor_accnum: Joi.string().allow("", null),
  guarantee_img1: Joi.any().allow("", null),
  guarantee_img2: Joi.any().allow("", null),
  guarantee_img3: Joi.any().allow("", null),
  device: Joi.string(),
});

export const ValidateLonOnlineDropdown = Joi.object({
  selecttable: Joi.string().required(),
});

export const ValidateLoanProvinceDropdown = Joi.object({
  pv_code: Joi.string(),
});

export const ValidateLoanDistrictDropdown = Joi.object({
  pv_code: Joi.number().required(),
});

export const ValidateLoanSubDistrictDropdown = Joi.object({
  amp_code: Joi.number().required(),
});

export const ValidateOCRScan = Joi.object({
  url_image: Joi.string().required(),
  type: Joi.string().required(),
});

export const ValidateUpdateAppAgreement = Joi.object({
  phone_firebase: Joi.string().required(),
  app_agree_status: Joi.string().required(),
});

export const ValidateUpdateAddressDigital = Joi.object({
  cust_code: Joi.string().required(),
  idcard: Joi.string().required(),
  address_number: Joi.string().allow("", null),
  village: Joi.string().allow("", null),
  sub_district: Joi.string().allow("", null),
  district: Joi.string().required(),
  province: Joi.string().required(),
  zipcode: Joi.string().allow("", null),
});

export const ValidateGuaranteeFromType = Joi.object({
  guarantee_type: Joi.string().required(),
});

export const ValidateGuaranteeSubTypeFromBrand = Joi.object({
  guarantee_type: Joi.string().required(),
  guarantee_brand_id: Joi.string().required(),
});

export const ValidateGuaranteeModel = Joi.object({
  guarantee_type: Joi.string().required(),
  guarantee_brand_id: Joi.string().required(),
  guarantee_subtype_id: Joi.string().required(),
});

export const ValidateCustcode = Joi.object({
  cust_code: Joi.string().required(),
});

export const ValidateBankName = Joi.object({
  bankName: Joi.string().required(),
});

export const ValidateGetBankData = Joi.object({
  idcard: Joi.string().allow("", null),
  phone_firebase: Joi.string().allow("", null),
});

export const ValidateUpdateFCMToken = Joi.object({
  token: Joi.string().required(),
  phone_firebase: Joi.string().required(),
  os: Joi.string().required(),
});

export const ValidateSearchHistoryMR = Joi.object({
  mr_code: Joi.string().required(),
  keyword: Joi.string().required(),
  offset: Joi.any().allow("", null),
});

export const ValidateSearchHistoryReferal = Joi.object({
  phone_firebase: Joi.any().required(),
  mr_code: Joi.any().allow("", null),
  ref_code: Joi.any().allow("", null),
  keyword: Joi.any().required(),
  offset: Joi.any().allow("", null),
});

export const ValidateDeleteAllNoti = Joi.object({
  phone_firebase: Joi.any().required(),
  user_running: Joi.any().required(),
});

export const ValidateSearchData = Joi.object({
  keyword: Joi.any().required(),
  offset: Joi.any().allow("", null),
});

export const ValidateQRPayments = Joi.object({
  from: Joi.string().required(),
  amount: Joi.any().required(),
  ctt_code: Joi.string().required(),
  phone: Joi.string().required(),
  branchPICO: Joi.any().allow("", null),
});

export const ValidateUploadBillPayments = Joi.object({
  pay_date: Joi.string().allow("", null),
  ctt_code: Joi.any().required(),
  cust_name: Joi.string().required(),
  cust_phone: Joi.string().required(),
  pt_name: Joi.any().required(),
  cpy_code: Joi.any().allow("", null),
  bill_img: Joi.any().required(),
  bill_type: Joi.any().required(),
  send_from: Joi.any().required(),
  branch: Joi.string().allow("", null),
});

export const ValidateUId = Joi.object({
  uid: Joi.any().required(),
});

export const ValidateUpdateLoginUser = Joi.object({
  uid: Joi.any().required(),
  phone_firebase: Joi.any().required(),
  account_name: Joi.string().allow("", null),
  account_img: Joi.string().allow("", null),
  account_email: Joi.string().allow("", null),
});

export const ValidateCheckFeedback = Joi.object({
  menu: Joi.any().required(),
  phone_firebase: Joi.any().required(),
});

export const ValidateMenuName = Joi.object({
  menu_name: Joi.any().required(),
});

export const ValidateFeedbackMultiType = Joi.object({
  feedbacks: Joi.any().required(),
  comments: Joi.any().allow("", null),
  from_menu: Joi.string().required(),
  image: Joi.string().allow("", null),
  os: Joi.string().allow("", null),
  fullname: Joi.string(),
  phone: Joi.string(),
  phone_firebase: Joi.string(),
});

export const ValidateAcceptRejectedStatus = Joi.object({
  grant_id: Joi.any().required(),
  user_id: Joi.any().required(),
  grant_status: Joi.string().allow("", null),
  reason: Joi.string().allow("", null),
});

export const ValidateUpdateBookBank = Joi.object({
  phone: Joi.string().required(),
  cust_name: Joi.string().required(),
  cust_code: Joi.string().allow("", null),
  bank_name: Joi.string().required(),
  bank_number: Joi.string().required(),
  bank_file: Joi.string().allow("", null),
  ctt_code: Joi.string().allow("", null),
});

export const ValidateUpdateRefCode = Joi.object({
  phone_firebase: Joi.string().required(),
  ref_code: Joi.string().required(),
});

export const ValidateGetReferralDownload = Joi.object({
  phone_firebase: Joi.string().required(),
  mr_code: Joi.string().allow("", null),
  ref_code: Joi.string().allow("", null),
});

export const ValidateSendNoti = Joi.object({
  phone: Joi.string().required(),
  fcm_noti_title: Joi.string().required(),
  fcm_noti_body: Joi.string().required(),
  display_title: Joi.string().required(),
  display_body: Joi.string().required(),
  notification_img: Joi.string().allow("", null),
  notification_type: Joi.string().allow("", null),
  fcm_noti_type: Joi.string().allow("", null),
});

export const ValidateDigitalContract = Joi.object({
  mn_grand: Joi.number().min(1).required(),
  periods: Joi.string().required(),
  guarantee_id: Joi.string().required(),
  cust_code: Joi.string().required(),
  ctt_date: Joi.string().required(),
  bank: Joi.string().required(),
  bank_branch: Joi.string().allow("", null),
  bank_account: Joi.string().pattern(/^\d+$/, "numbers").required(),
});

export const ValidateArCardPreview = Joi.object({
  mn_grand: Joi.number().min(1).required(),
  periods: Joi.string().required(),
  ctt_date: Joi.string().required(),
});

export const ValidateLogDigitalLoan = Joi.object({
  msg: Joi.string().required(),
  name: Joi.string().allow("", null),
  phone: Joi.string().allow("", null),
  ctt_code: Joi.string().allow("", null),
  cust_code: Joi.string().allow("", null),
  time: Joi.string().allow("", null),
});

export const ValidateMessageTGGroup = Joi.object({
  message: Joi.string().required(),
  group_name: Joi.any().required(),
});

export const ValidateUpdateDataByField = Joi.object({
  fields: Joi.string().required(),
  cust_data: Joi.string().required(),
  cust_phone: Joi.string().required(),
});

export const ValidateSendNotiByTopic = Joi.object({
  noti_title: Joi.string().required(),
  noti_body: Joi.string().required(),
  noti_topic: Joi.string().required(),
  noti_type: Joi.string().allow("", null),
});

export const ValidateCarModelFromYear = Joi.object({
  year: Joi.string().required(),
  carb_name_en: Joi.string().required(),
  carb_id: Joi.string().required(),
});


export const ValidateSelectVillage = Joi.object({
  tum_code: Joi.any().required(),
});

export const ValidateLogmenu = Joi.object({
  phone: Joi.string().required(),
  user_running: Joi.any().required(),
  menuId: Joi.any().required(),
  platform: Joi.string().allow("", null)
});