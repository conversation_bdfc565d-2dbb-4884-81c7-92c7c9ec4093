import { convertThenDetectQueryType, DetectQueryType } from "../../help/convertOptionMysqlClient";
import { MysqlClient, MysqlClientHandler } from "../../help/mysqlClient";
import {
  convertPhoneNumber,
  formatPhoneNumber,
} from "../../help/formatPhoneData";
import { OnlyPhoneFirebase, OnlySearchData } from "../../type/productType";

const getBranchData = async (payload: OnlyPhoneFirebase) => {
  try {
    var SQL1 = "";
    var orderEncyprtDB = false;
    var Database: string = "";
    var phone_number: string = "";
    var arr: any = [];

    switch (BUNAME) {
      case "rplc":
        SQL1 = "SELECT * FROM tb_branch_RPLC WHERE show_status = 'Y'";
        Database = MainDB;
        break;
      case "rafco":
        SQL1 = "SELECT * FROM tb_branch WHERE show_status = 'Y'";
        Database = MainDB;
        break;
      default:
        SQL1 = "SELECT * FROM tb_branch WHERE show_status = 'Y'";
        Database = MainDB;
        break;
    }

    var options = await convertThenDetectQueryType(
      Database,
      SQL1,
      orderEncyprtDB,
      [],
      []
    );
    var response = await new MysqlClient(options?.[0]).connect(...options?.[1]);

    if (response != false && response.length > 0) {
      for (var i = 0; i < response.length; i++) {
        var data = {};
        if (BUNAME == "aam") {
          data = {
            branch_name: "สาขา" + response[i].branch_name_TH,
            branch_name_th: "สาขา" + response[i].branch_name_TH,
            branch_name_en: "สาขา" + response[i].branch_name_TH,
            branch_id: response[i].branch_name,
            province: response[i].branch_province,
            zone: response[i].branch_zone,
            address: response[i].address,
            address_th: response[i].address,
            address_en: response[i].address_EN,
            address_desc: response[i].address_desc,
            branch_phone: await formatPhoneNumber(response[i].mobile),
            line_id: response[i].line_id,
            facebook_id: response[i].facebook_id,
            facebook_url: "",
            telegram_id: "",
            whatsapp_id: "",
            map_url: response[i].Gmap_point,
            latitude: response[i].Latitude,
            longitude: response[i].Longitude,
          };
        } else if (BUNAME == "rplc") {
          data = {
            branch_name: response[i].branch_name,
            branch_name_th: response[i].branch_name_TH,
            branch_name_en: response[i].branch_name_EN,
            branch_id: response[i].bo_name_code,
            province: response[i].branch_province,
            zone: "",
            address: response[i].address,
            address_th: response[i].address_TH,
            address_en: response[i].address_EN,
            address_desc: response[i].address_details,
            branch_phone: await formatPhoneNumber(response[i].phone),
            line_id: response[i].line_id,
            facebook_id: response[i].facebook_id,
            facebook_url: "",
            telegram_id: "",
            whatsapp_id: response[i].whatsapp_id,
            map_url: response[i].map_url,
            latitude: response[i].latitude,
            longitude: response[i].longitude,
          };
        } else if (BUNAME == "rafco") {
          data = {
            branch_name: response[i].branch_name_KM,
            branch_name_th: response[i].branch_name_TH,
            branch_name_en: response[i].branch_name_EN,
            branch_id: response[i].branch_name,
            province: response[i].branch_province,
            zone: response[i].branch_zone,
            address: response[i].address,
            address_th: response[i].address_TH,
            address_en: response[i].address_EN,
            address_desc: response[i].address_desc,
            branch_phone: await formatPhoneNumber(response[i].mobile),
            line_id: response[i].line_id,
            facebook_id: response[i].facebook_id,
            facebook_url: "",
            telegram_id: response[i].telegram_id,
            whatsapp_id: "",
            map_url: response[i].Gmap_link,
            latitude: response[i].Latitude,
            longitude: response[i].Longitude,
          };
        }
        arr.push(data);
      }
      return arr;
    } else {
      return 0;
    }
  } catch (e) {
    console.log(e);
    return 0;
  }
};

const searchBranchData = async (payload: OnlySearchData) => {
  try {
    var SQL1 = "";
    var orderEncyprtDB = false;
    var Database: string = "";
    var phone_number: string = "";
    var arr: any = [];
    var params: any = [];
    var keyword: string = `%${payload.keyword}%`;
    var offset: number = parseInt(payload.offset.toString(), 10);
    var table_name: string = "";

    switch (BUNAME) {
      case "rplc":
        SQL1 =
          "SELECT * FROM tb_branch_RPLC WHERE (branch_name LIKE ? OR branch_name_TH LIKE ? OR branch_name_EN LIKE ?) LIMIT 5 OFFSET ?";
        Database = MainDB;
        params = [keyword, keyword, keyword, offset];
        table_name = "tb_branch_RPLC";
        break;
      case "rafco":
        SQL1 =
          "SELECT * FROM tb_branch WHERE (branch_name_KM LIKE ? OR branch_name_TH LIKE ? OR branch_name_EN LIKE ?) LIMIT 5 OFFSET ?";
        Database = MainDB;
        params = [keyword, keyword, keyword, offset];
        table_name = "tb_branch";
        break;
      default:
        SQL1 =
          "SELECT * FROM tb_branch WHERE branch_name_TH LIKE ? LIMIT 5 OFFSET ?";
        Database = MainDB;
        params = [keyword, offset];
        table_name = "tb_branch";
        break;
    }


    var options = await DetectQueryType(
      Database,
      SQL1,
      false,
      params,
      []
    );

    var response = await new MysqlClientHandler(options?.[0]).connect(
      ...options?.[1]
    );

    // const options = {
    //   entable: [
    //     {
    //       table: table_name,
    //       fields: [],
    //     },
    //   ],

    //   secret: DB_SECRET,
    // };

    // const MySQLCon = new MysqlClient(options);

    // var response = await MySQLCon.connect(Database, SQL1, params, table_name);

    if (response != false && response.length > 0) {
      for (var i = 0; i < response.length; i++) {
        var data = {};
        if (BUNAME == "aam") {
          data = {
            branch_name: "สาขา" + response[i].branch_name_TH,
            branch_name_th: "สาขา" + response[i].branch_name_TH,
            branch_name_en: "สาขา" + response[i].branch_name_TH,
            branch_id: response[i].branch_name,
            province: response[i].branch_province,
            zone: response[i].branch_zone,
            address: response[i].address,
            address_th: response[i].address,
            address_en: response[i].address_EN,
            address_desc: response[i].address_desc,
            branch_phone: await formatPhoneNumber(response[i].mobile),
            line_id: response[i].line_id,
            facebook_id: response[i].facebook_id,
            facebook_url: "",
            telegram_id: "",
            whatsapp_id: "",
            map_url: response[i].Gmap_point,
            latitude: response[i].Latitude,
            longitude: response[i].Longitude,
          };
        } else if (BUNAME == "rplc") {
          data = {
            branch_name: response[i].branch_name,
            branch_name_th: response[i].branch_name_TH,
            branch_name_en: response[i].branch_name_EN,
            branch_id: response[i].bo_name_code,
            province: response[i].branch_province,
            zone: "",
            address: response[i].address,
            address_th: response[i].address_TH,
            address_en: response[i].address_EN,
            address_desc: response[i].address_details,
            branch_phone: await formatPhoneNumber(response[i].phone),
            line_id: response[i].line_id,
            facebook_id: response[i].facebook_id,
            facebook_url: "",
            telegram_id: "",
            whatsapp_id: response[i].whatsapp_id,
            map_url: response[i].map_url,
            latitude: response[i].latitude,
            longitude: response[i].longitude,
          };
        } else if (BUNAME == "rafco") {
          data = {
            branch_name: response[i].branch_name_KM,
            branch_name_th: response[i].branch_name_TH,
            branch_name_en: response[i].branch_name_EN,
            branch_id: response[i].branch_name,
            province: response[i].branch_province,
            zone: response[i].branch_zone,
            address: response[i].address,
            address_th: response[i].address_TH,
            address_en: response[i].address_EN,
            address_desc: response[i].address_desc,
            branch_phone: await formatPhoneNumber(response[i].mobile),
            line_id: response[i].line_id,
            facebook_id: response[i].facebook_id,
            facebook_url: "",
            telegram_id: response[i].telegram_id,
            whatsapp_id: "",
            map_url: response[i].Gmap_link,
            latitude: response[i].Latitude,
            longitude: response[i].Longitude,
          };
        }
        arr.push(data);
      }
      return arr;
    } else {
      return 0;
    }
  } catch (e) {
    console.log(e);
    return 0;
  }
};

function formatPhoneNumber(input: string): string {
  // แยกสตริงตามเครื่องหมายจุลภาคและช่องว่าง
  const numbers = input.split(",").map((num) => num.trim());

  // เลือกเฉพาะหมายเลขแรกและลบขีดออก (ถ้ามี)
  const firstNumber = numbers[0].replace(/-/g, "");

  return firstNumber;
}

export { getBranchData, searchBranchData };
