import { convertThenDetectQueryType } from "../../help/convertOptionMysqlClient";
import { MysqlClient } from "../../help/mysqlClient";
import { responseError } from "../../help/response";
import { getBranchData } from "../../help/branch";
import { OnlySaveRequestLoan } from "../../type/productType";
import { sendNotiTG } from "../../help/sendNotificationMapp";
import { formatDateNumber } from "../../help/formatDate";

import {
  formatPhoneNumber,
  convertPhoneNumber,
} from "../../help/formatPhoneData";
import { formatInTimeZone } from "date-fns-tz";

/*
 อ่านก่อนร้อง
 
 ######  #######    #    ######     #     # #######    ### ### 
 #     # #         # #   #     #    ##   ## #          ### ### 
 #     # #        #   #  #     #    # # # # #          ### ### 
 ######  #####   #     # #     #    #  #  # #####       #   #  
 #   #   #       ####### #     #    #     # #                  
 #    #  #       #     # #     #    #     # #          ### ### 
 #     # ####### #     # ######     #     # #######    ### ### 


SLQ query ในแต่ละ BU ไม่เหมือนกัน

MainDB ให้ไปแก้ ฐานข้อมูลในหน้า index ตรง global variable
*/

const saveRequestLoan = async (payload: OnlySaveRequestLoan) => {
  try {
    var SQL1 = "";
    var SQL2 = "";
    var orderEncyprtDB = false;
    var Database = "";
    let params: any[] = [];
    var branch_id = "";
    var fullname: string = payload.firstname + " " + payload.lastname;
    var guarantee_type_name: string = "";

    if (payload.branch == "" || payload.branch === "null") {
      // หาข้อมูลสาขา
      branch_id = await getBranchData(payload.district, payload.province);
    } else {
      branch_id = payload.branch;
    }
    const now = new Date(Date.now()).toLocaleString("th-TH", {
      timeZone: "Asia/Bangkok",
    });
    var message: string = "";

    // --- ระบุฟิลด์ และแยกประเภท ข้อมูล ก่อน insert ข้อมูลลงฐาน
    const response_data = handleInsertRequest(
      BUNAME,
      fullname,
      payload,
      branch_id
    );

    SQL1 = response_data.SQL1;
    Database = response_data.Database;
    guarantee_type_name = response_data.guarantee_type_name; // ชื่อหลักประกันที่ detect ของแต่ละประเทศแล้ว
    // console.log({ SQL1, Database, params });

    //-- เช็คเวลาแนะนำจาก elastic เฉพาะ AAM
    if (BUNAME == "aam") {
      // --- check time request is more than 30 munite?
      var chk_time = await checkTimeRequest(payload, guarantee_type_name);
      if (chk_time == false) {
        //-- เวลาน้อยกว่า 30 นาที ยังแนะนำไม่ได้
        return 0;
      }
    }

    console.log("save data to data center 4 ไม่ 5 ดึง ");
    var data_customer = await generate4NoRunning(
      payload,
      guarantee_type_name,
      branch_id
    ); // บันทึกลงฐาน 4 ไม่

    if (data_customer != false) {
      // detect parameter สำหรับ tb_loan RPLC, RAFCO
      params = createSQLParams(
        fullname,
        payload,
        guarantee_type_name,
        branch_id,
        data_customer.running_4no
      );

      // -- บันทึกข้อมูลลง AMS4 THAI , AMS4 RPLC, AMS4 RAFCO
      console.log(BUNAME);
      console.log("save data to ams4 tbwalkin");
      var response_ams4 = await saveDataRequestLoan(
        payload,
        data_customer,
        guarantee_type_name
      ); // บันทึกลงฐาน tbwalkin ams4

      if (response_ams4 != false) {
        console.log("save data to ams4 tbwalkin success");
        //---  บันทึกข้อมูลลง local database ของแต่ละแอป ex. AAM -> elastic , rplc, rafco -> webRPLC, MAppRAFCO -> tb_requestLoan
        if (BUNAME == "aam") {
          //-- บันทึกลง elastic
          saveDataToElastic(payload, guarantee_type_name);
          message =
            "\n 🧰 ลูกค้าสนใจขอสินเชื่อ ผ่าน Mapp AAM 🧰 \n\n 📈📈📈\n\n 📝 ชื่อลูกค้า : " +
            fullname +
            "\n 📱 เบอร์ติดต่อกลับลูกค้า : " +
            payload.phone +
            "\n 🚗 ประเภทหลักค้ำ : " +
            guarantee_type_name +
            "\n 💰 วงเงินที่ต้องการกู้ : " +
            payload.request_loan +
            "\n 💲 ต้องการผ่อนต่อเดือน : " +
            "\n 🏠 ลูกค้าต้องการติดต่อที่ : " +
            payload.district +
            " " +
            "จ." +
            payload.province +
            "\n  ↪ โปรดตรวจสอบรายการใน   AMS4" +
            "\n (หน้ารายงานการรับลูกค้าเบื้องต้น)";
        } else if (BUNAME == "rplc" || BUNAME == "rafco") {
          //-- บันทึกลง webRPLC , MAppRAFCO -> tb_requestLoan
          var options = await convertThenDetectQueryType(
            Database,
            SQL1,
            orderEncyprtDB,
            params,
            []
          );
          var saveRequestLoan = await new MysqlClient(options?.[0]).connect(
            ...options?.[1]
          );

          if (saveRequestLoan == false) {
            console.log("บันทึกข้อมูลลงฐาน " + BUNAME + " ไม่สำเร็จ");
          }

          if (BUNAME == "rplc") {
            message =
              "📌 แจ้งเตือนจาก Mapp RPLC\nขณะนี้มีลูกค้าใช้บริการเมนู ขอสินเชื่อ \n📱 เบอร์ติดต่อกลับลูกค้า : " +
              payload.phone +
              " \n👉ชื่อลูกค้า : " +
              fullname +
              " \n🚗 ประเภทหลักประกัน : " +
              guarantee_type_name +
              " \n💰 วงเงินขอสินเชื่อ : " +
              payload.request_loan +
              " \n👉สาขา : " +
              branch_id +
              " \n👉Link รูปหลักประกัน (ไม่บังคับ) : " +
              payload.image +
              " \n🕐 เวลา : " +
              now +
              " \n*รบกวนสาขาติดต่อกลับหาลูกค้าภายใน 15 นาที !!";
          } else if (BUNAME == "rafco") {
            message =
              "💸 แจ้งเตือน ลูกค้าสนใจจัดสินเชื่อ \n🔔 Mobile App RAFCO \n🎡 ชื่อลูกค้า : " +
              fullname +
              "\n📱 เบอร์โทรศัพท์ : " +
              payload.phone +
              "\n📍จังหวัด : " +
              payload.province +
              "\n📍 สาขา : " +
              payload.district +
              "\n🎡ประเภทหลักประกัน : " +
              guarantee_type_name +
              "\n💸วงเงินที่ต้องการ : " +
              payload.request_loan +
              " $\n🚀 Link รูปหลักประกัน : " +
              payload.image +
              "\n🕐 เวลา : " +
              now +
              " \n  ***กรุณาติดต่อลูกค้าด้วยนะครับ*** \n  ขอบคุณครับ";
          }
        }
      } else {
        console.log("error : save data to ams4 tbwalkin failed");
        return 0;
      }
    } else {
      console.log("error : บันทึกลงฐาน 4 ไม่ ไม่สำเร็จ");
      return 0;
    }
    if (BUNAME == "aam") {
      const groupIds = ["Mappทุกเรื่องราว", branch_ID];
      await Promise.all(groupIds.map((id) => sendNotiTG(message, id)));
    } else {
      await sendNotiTG(message, "");
    }

    return "success";
  } catch (e) {
    console.log(e);
    return await responseError(e, 404);
  }
};

function handleInsertRequest(
  BUNAME: string,
  fullname: string,
  payload: any,
  branch_id: string
) {
  let SQL1 = "";
  let Database = "";
  let params: any[] = [];
  const guarantee_type_name = getGuaranteeTypeName(
    BUNAME,
    payload.guarantee_type
  );

  // ถ้า prod ฐานจริงต้องเพิ่ม ฟิลด์ installment_period ในฐาน tb_requestLoan ก่อน
  if (BUNAME === "rplc" || BUNAME === "rafco") {
    SQL1 =
      "INSERT INTO tb_requestLoan (Name, phone, phoneFirebase, amout_Money, installment_period, car_type, branch_code, district, city, image,	running_4no ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    Database = MainDB;
  }

  return { SQL1, Database, params, guarantee_type_name };
}

function getGuaranteeTypeName(BUNAME: string, guaranteeType: string): string {
  const typeMap: { [key: string]: { [key: string]: string } } = {
    rplc: {
      car: "ລົດໃຫຍ່",
      threevehicle: "ລົດໃຫຍ່",
      motocycle: "ລົດຈັກ",
      land: "ທີ່ດີນ",
    },
    rafco: {
      car: "Car",
      threevehicle: "Three Vehicle",
      motocycle: "Motorcycle",
      land: "Land",
    },
    default: {
      car: "รถยนต์",
      truck: "รถยนต์",
      motocycle: "มอเตอร์ไซค์",
      land: "ที่ดิน",
    },
  };

  return (
    typeMap[BUNAME]?.[guaranteeType] ?? typeMap.default[guaranteeType] ?? ""
  );
}

function createSQLParams(
  fullname: string,
  payload: any,
  guaranteeTypeName: string,
  branch_id: string,
  running_4no: string
): any[] {
  return [
    fullname,
    payload.phone,
    payload.phone_firebase,
    payload.request_loan,
    payload.request_installment,
    guaranteeTypeName,
    branch_id,
    payload.district,
    payload.province,
    payload.image,
    running_4no,
  ];
}

async function checkTimeRequest(params, guarantee_type_name) {
  try {
    var body = { phone: params.phone };

    const newRequest = new Request(
      "https://64ca2eqp3m.execute-api.ap-southeast-1.amazonaws.com/latest/chk_requestloan",
      {
        body: JSON.stringify(body),
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-api-key": HCS_X_API,
        },
        method: "POST",
      }
    );

    const res_data = await fetch(newRequest);

    var response = await res_data.json();
    //พบข้อมูลs
    if (response.statusCode === 200) {
      let update_date: string = response.data[0].update_date;
      const update_time: string = response.data[0].update_time;

      let dateComponents: string[] = update_date.split("/");
      let year: string = dateComponents[2];
      let month: string = dateComponents[1];
      let day: string = dateComponents[0];
      let newDate: string = year + "-" + month + "-" + day;

      var last_time: string = newDate + " " + update_time;

      const timestamp: number = response.data[0].timestamp;

      // แปลง timestamp เป็น Date object
      const test_date: Date = new Date(timestamp * 1000);

      // ตั้งค่าเขตเวลา
      const options: Intl.DateTimeFormatOptions = {
        timeZone: "Asia/Jakarta",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      };

      // แปลงวันที่เป็นรูปแบบ "2024-07-12 09:04:45"
      const formatter = new Intl.DateTimeFormat("en-EN", options);
      const parts = formatter.formatToParts(test_date);
      const lastTime = `${parts.find((p) => p.type === "year")?.value}-${
        parts.find((p) => p.type === "month")?.value
      }-${parts.find((p) => p.type === "day")?.value} ${
        parts.find((p) => p.type === "hour")?.value
      }:${parts.find((p) => p.type === "minute")?.value}:${
        parts.find((p) => p.type === "second")?.value
      }`;

      // -- หาเวลาปัจจุบัน
      const options2 = {
        timeZone: "Asia/Jakarta",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      };

      const formatter2 = new Intl.DateTimeFormat("en-EN", options2);
      const now = new Date();
      const formattedDate = formatter2.formatToParts(now);

      const currentTime = `${
        formattedDate.find((p) => p.type === "year")?.value
      }-${formattedDate.find((p) => p.type === "month")?.value}-${
        formattedDate.find((p) => p.type === "day")?.value
      } ${formattedDate.find((p) => p.type === "hour")?.value}:${
        formattedDate.find((p) => p.type === "minute")?.value
      }:${formattedDate.find((p) => p.type === "second")?.value}`;

      var currentDate: number = new Date(currentTime).getTime();
      var targetDate: number = new Date(lastTime).getTime();

      // console.log(currentDate);
      // console.log(targetDate);

      var diffTime: number = currentDate - targetDate;
      var diffMinutes: number = diffTime / 60000;
      console.log(diffMinutes);

      if (diffMinutes > 30.0) {
        return true;
      } else {
        console.log("น้อยกว่า 30 นาที");
        //return 404
        return false;
      }
    } else {
      return true;
    }
  } catch (error) {
    console.log(error);
    return false;
  }
}

async function generate4NoRunning(params, guarantee_name, branch) {
  try {
    var SQL: string = "";
    var param: any = [];
    var Database: string = "";
    var branch_id: string = branch;
    var fullname: string = params.firstname + " " + params.lastname;
    var firstname: string = params.firstname;
    var lastname: string = params.lastname;
    var phone: string = "";
    var line: string = "";
    var facebook: string = "";
    var note9: string = "";
    var note7: string = "";
    var product_aam: string = "";

    var note1: string = "NM ออนไลน์ Mapp";
    var detail: string = "สนใจขอข้อมูลสินเชื่อ";
    var data_send: string = "สนใจเข้าแกรน";
    var data_name: string = "";
    var pursuit_channel: string = "";
    var note2: string = "ใหม่";
    var BU: string = "";
    var not_four: string = "แสวงหา";
    var title: string = "";

    const currentDate: Date = new Date();
    const year: number = currentDate.getFullYear();
    const month: number = currentDate.getMonth() + 1; //getMonth() ดึงเดือนปัจจุบัน (0-indexed) เพิ่ม 1 เพราะเดือนเริ่มจาก 1
    const formattedDatete: string = `${year.toString().substring(2)}${month
      .toString()
      .padStart(2, "0")}`;
    var pro_id: string = "";
    var pu_id: string = "";
    const dateformat = await formatDateNumber(currentDate);


    // P1-AAM-LBR-PROAAM006-24050001
    var running_4no: string = "";

    switch (BUNAME) {
      case "rplc":
        phone = await convertPhoneNumber(params.phone_firebase);
        note7 = "จัดไฟแนนซ์";
        if (guarantee_name == "ລົດໃຫຍ່") {
          product_aam = "รถยนต์";
        } else if (guarantee_name == "ລົດຈັກ") {
          product_aam = "มอเตอร์ไซค์";
        }
        data_name = "สื่อการตลาด Mapp";
        pursuit_channel = "MappRPLC";
        BU = "RPLC";
        title = "ผลิตภัณฑ์รวม RPLC";
        pro_id = "PRORPLC001";
        pu_id =
          "P1-RPLC-" +
          branch_id +
          "-" +
          pro_id +
          "-" +
          formattedDatete +
          "0001";
        Database = DB_BCT_Datacenter_RPLC;
        SQL =
          "INSERT INTO activity (first_lastName,firstName,lastName,mobilePhone,BU,pursuit_channel,note1,note2,create_user,assign,product_aam,data_send,data_name,title,not_four,pu_id,details,note7,data_send_timestamp) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        param = [
          fullname,
          firstname,
          lastname,
          phone,
          BU,
          pursuit_channel,
          note1,
          note2,
          "Mapp",
          branch_id,
          product_aam,
          data_send,
          data_name,
          title,
          not_four,
          pu_id,
          detail,
          note7,
          dateformat
        ];
        break;
      case "rafco":
        phone = await convertPhoneNumber(params.phone_firebase);

        if (guarantee_name == "Car") {
          product_aam = "รถยนต์";
        } else if (guarantee_name == "Motorcycle") {
          product_aam = "มอเตอร์ไซต์";
        } else if (guarantee_name == "Three Vehicle") {
          product_aam = "รถสามล้อ";
        } else if (guarantee_name == "Land") {
          product_aam = "ที่ดิน";
        }
        data_name = "สื่อการตลาด";
        pursuit_channel = "MappRAFCO";
        BU = "RAFCO";
        title = "ผลิตภัณฑ์รวมRAFCO";
        pro_id = "PRORAFCO001";
        pu_id =
          "P1-RAFCO-" +
          branch_id +
          "-" +
          pro_id +
          "-" +
          formattedDatete +
          "0001";
        note9 = "จัดไฟแนนซ์";
        const dateTZ = formatInTimeZone(
          Date.now(),
          "Asia/Bangkok",
          "yyyy-MM-dd HH:mm:ss"
        );

        Database = DB_BCT_Datacenter_RAFCO;
        SQL =
          "INSERT INTO activity (first_lastName,firstName,lastName,mobilePhone,note1,details,data_send,data_name,pursuit_channel,note2,BU,title,not_four,product_aam,pu_id,note9,date_contact,assign,data_send_timestamp) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        param = [
          fullname,
          firstname,
          lastname,
          phone,
          note1,
          detail,
          data_send,
          data_name,
          pursuit_channel,
          note2,
          BU,
          title,
          not_four,
          product_aam,
          pu_id,
          note9,
          dateTZ,
          branch_id,
          dateformat
        ];
        break;
      default:
        phone = await formatPhoneNumber(params.phone_firebase);
        note9 = "จัดไฟแนนซ์";
        product_aam = guarantee_name;
        data_name = "สื่อการตลาด";
        pursuit_channel = "MappAAM";
        BU = "AAM";
        title = "ผลิตภัณฑ์รวม AAM";
        pro_id = "PROAAM006";
        pu_id =
          "P1-AAM-" + branch_id + "-" + pro_id + "-" + formattedDatete + "0001";
        Database = DB_BCT_Datacenter;
        SQL =
          "INSERT INTO activity (first_lastName,firstName,lastName,mobilePhone,LINE,facebook,note9,product_aam,note1,assign,details,data_send,data_name,pursuit_channel,note2,BU,not_four,title,pu_id,data_send_timestamp) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        param = [
          fullname,
          firstname,
          lastname,
          phone,
          line,
          facebook,
          note9,
          product_aam,
          note1,
          branch_id,
          detail,
          data_send,
          data_name,
          pursuit_channel,
          note2,
          BU,
          not_four,
          title,
          pu_id,
          dateformat
        ];
        break;
    }

    var options = await convertThenDetectQueryType(
      Database,
      SQL,
      false,
      param,
      []
    );
    var response = await new MysqlClient(options?.[0]).connect(...options?.[1]);

    if (response != false) {
      console.log("บันทึกข้อมูล 4 ไม่ สำเร็จ");

      running_4no = response.lastInsertId;

      return {
        status: true,
        running_4no: running_4no,
        phone: phone,
        branch: branch_id,
      };
    } else {
      console.log("บันทึกข้อมูล 4 ไม่ ไม่สำเร็จ");
      return false;
    }
  } catch (error) {
    console.log(error);
    return 0;
  }
}

async function saveDataRequestLoan(dataRequestLoan, data4no, guarantee_name) {
  try {
    var SQL: string = "";
    var params: any = [];
    var Database: string = "";
    var create_user: string = "Mapp";
    var gcm_code: string = "";
    var gcs_code: string = "";
    var bo_name_code: string = data4no.branch;
    var customer_type: string = "ลูกค้าใหม่";
    var product: string = "";
    var cpy_code: string = "";

    // console.log(`guarantee_name : ${guarantee_name}`);

    switch (BUNAME) {
      case "rplc":
        if (guarantee_name == "ລົດໃຫຍ່") {
          product = "1|รถยนต์";
        } else if (guarantee_name == "ລົດຈັກ") {
          product = "2|มอเตอร์ไซค์";
        } else if (guarantee_name == "ທີ່ດີນ") {
          product = "3|ที่ดิน";
        } else {
          product = "3|ใบตาดิน";
        }

        gcm_code = "MM";
        gcs_code = "NM";

        Database = DB_AMS4_RPLC;
        SQL =
          "INSERT INTO tbwalkin (create_user, firstName, lastName, mobile, customer_type, gcm_code, gcs_code, product, bo_name_code,loan_amount,loan_period, running_4no, cpy_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        params = [
          create_user,
          dataRequestLoan.firstname,
          dataRequestLoan.lastname,
          await formatPhoneNumber(dataRequestLoan.phone_firebase),
          customer_type,
          gcm_code,
          gcs_code,
          product,
          bo_name_code,
          dataRequestLoan.request_loan,
          dataRequestLoan.request_installment,
          data4no.running_4no,
          "RPLC",
        ];

        // console.log("#######");
        // console.log(params);

        break;
      case "aam":
        if (guarantee_name == "รถยนต์") {
          product = "1|รถยนต์";
          cpy_code = "AAM";
        } else if (guarantee_name == "มอเตอร์ไซค์") {
          product = "2|มอเตอร์ไซค์";
          cpy_code = "ACM";
        } else if (guarantee_name == "ที่ดิน") {
          product = "3|ที่ดิน";
          cpy_code = "AAM";
        }

        gcm_code = "NQ";
        gcs_code = "MM,NM";

        Database = DB_AMS4;
        SQL =
          "INSERT INTO tbwalkin (create_user,firstName,lastName,mobile,customer_type,product,bo_name_code,loan_amount,loan_period,cpy_code,running_4no,gcm_code,gcs_code) VALUES (?,?,?,?,?,?,?,?,?,?,?)";
        params = [
          create_user,
          dataRequestLoan.firstname,
          dataRequestLoan.lastname,
          data4no.phone,
          customer_type,
          product,
          bo_name_code,
          dataRequestLoan.request_loan,
          dataRequestLoan.request_installment,
          cpy_code,
          data4no.running_4no,
          gcm_code,
          gcs_code,
        ];
        break;
      default:
        //rafco
        if (guarantee_name == "Car") {
          product = "1|รถยนต์";
        } else if (guarantee_name == "Motorcycle") {
          product = "2|มอเตอร์ไซค์";
        } else if (guarantee_name == "Land") {
          product = "3|ที่ดิน";
        } else if (guarantee_name == "Three Vehicle") {
          product = "4|รถสามล้อ";
        }

        gcm_code = "MM";
        gcs_code = "NM";

        Database = DB_AMS4_RAFCO;
        SQL =
          "INSERT INTO tbwalkin (create_user, firstName, lastName, mobile, customer_type, gcm_code, gcs_code, product, bo_name_code,loan_amount,loan_period, running_4no, cpy_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        params = [
          create_user,
          dataRequestLoan.firstname,
          dataRequestLoan.lastname,
          await formatPhoneNumber(dataRequestLoan.phone_firebase),
          customer_type,
          gcm_code,
          gcs_code,
          product,
          bo_name_code,
          dataRequestLoan.request_loan,
          dataRequestLoan.request_installment,
          data4no.running_4no,
          "RAFCO",
        ];
        break;
    }

    var options = await convertThenDetectQueryType(
      Database,
      SQL,
      false,
      params,
      []
    );
    var response = await new MysqlClient(options?.[0]).connect(...options?.[1]);

    if (response != false) {
      console.log("save data ams4 success");
      return true;
    } else {
      console.log("error save fail");
      return false;
    }
  } catch (error) {
    console.log(error);
    return 0;
  }
}

async function saveDataToElastic(params, guarantee_type_name) {
  try {
    var sort = Math.floor(Date.now() / 1000);
    var idUpdate: string = ""; //ไอดีสำหรับอัพเดท
    var id; // ไอดี
    if (idUpdate == undefined || idUpdate == "") {
      id = params.phone + sort;
    } else {
      id = idUpdate;
    }

    // body Data เช็คเวลาบันทึกเข้า elastic ครั้งล่าสุด
    var data_elastic: any = {
      firstname: params.firstname,
      lastname: params.lastname,
      phone: params.phone,
      line: params.line,
      facebook: params.facebook,
      idcard: params.idcard,
      guarantee_type: guarantee_type_name,
      province: params.province,
      cost_estimate: "-",
      request_loan: params.request_loan,
      request_installment: params.request_installment,
      contact_branch: params.district,
      img_1: params.image,
      req_type: "-",
      registration_book: "-",
      id: id,
      sort: sort,
    };

    const newRequest = new Request(
      "https://64ca2eqp3m.execute-api.ap-southeast-1.amazonaws.com/latest/save_requestloan",
      {
        body: JSON.stringify(data_elastic),
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-api-key": HCS_X_API,
        },
        method: "POST",
      }
    );

    const response = await fetch(newRequest);

    var responseData = await response.json();
    if (responseData.statusCode === 200) {
      return true;
    } else {
      console.log("บันทึกข้อมูลลง elastic ไม่สำเร็จ");
      return false;
    }
  } catch (error) {
    console.log(error);
  }
}

export { saveRequestLoan };
