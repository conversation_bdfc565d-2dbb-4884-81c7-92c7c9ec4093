const sendNotiTG = async (message : string, groupname : string) => {
    try {
      console.log(":sendNotiTG");
      
        var url;
        var groupid;
        switch(BUNAME){
            case "rplc":
                url = "https://api.telegram.org/bot5059687928:AAHDlsBAFAnLTbciH8N_dUVwFDrBcf_KeZg/sendMessage";
                if(groupname == "kyc_bank"){
                  groupid = "-**********";
                }else if(groupname == "rplc_pay"){
                  groupid = "-**********";
                }else{
                  groupid = "-*************"; //production
                }
                // groupid = "-**********"; // BETA TEST
                break;
            case "rafco":
                url = "https://api.telegram.org/bot5059687928:AAHDlsBAFAnLTbciH8N_dUVwFDrBcf_KeZg/sendMessage";
                groupid = "-*************";
                // groupid = "-**********"; // BETA TEST
                break;
            default:
                url = "https://api.telegram.org/bot5059687928:AAHDlsBAFAnLTbciH8N_dUVwFDrBcf_KeZg/sendMessage";
                if (groupname == "Mappทุกเรื่องราว") {
                    groupid = "-*************";
                    //groupid = "-*********";
                  } else if (groupname == "MR+") {
                    groupid = "-*************";
                  } else if (groupname == "FXMapp") {
                    groupid = "-1001580810278";
                  } else if (groupname == "KycAAM") {
                    groupid = "-1001521313883";
                  } else if (groupname == "MCM-MR") {
                    groupid = "-781030572";
                  } else if (groupname == "digiloan") {
                    groupid = "-1001614019433";
                  } else if (groupname == "digitallog") {
                    groupid = "-1001740296967";
                  } else if (groupname == "KycSumsub") {
                    groupid = "-854613604";
                  }else if (groupname == "champtest") {
                    groupid =  "-826110308";
                  }else if(groupname == 'Zone1'){
                    groupid = "-1001637027352";
                  }
                  // ห้องสาขา
                  else if(groupname == 'NAY' || groupname == 'TKK' || groupname == 'TYK' || groupname == 'SOD'){
                    groupid = "-1001544244982"; //เขต 2
                  }else if(groupname == 'SKL' || groupname == 'WNY' || groupname == 'BRM' || groupname == 'SUR'){
                    groupid = "-1001641108290"; //เขต 3
                  }else if(groupname == 'KBB' || groupname == 'PCL' || groupname == 'CHS'){
                    groupid = "-1001270796459"; //เขต 4
                  }else if(groupname == 'NKY' || groupname == 'SRB' || groupname == 'NKR' || groupname == 'LBR' ){
                    groupid = "-1001710335871"; //เขต 5
                  }else if(groupname == 'NTR' || groupname == 'SNR' || groupname == 'RBR' || groupname == 'KAN' ){
                    groupid = "-1001703797327"; //เขต 6
                  }else if(groupname == 'PTY' || groupname == 'RYL' || groupname == 'STH' || groupname == 'PLD' || groupname == 'PYJ' ){
                    groupid = "-1001770590388"; //เขต 7
                  }else if(groupname == 'NOK' || groupname == 'BUK' || groupname == 'SKN' || groupname == 'UDD' ){
                    groupid = "-1001734069920"; //เขต 8
                  }else if(groupname == 'feedback'){
                    groupid = "-1001893927719"; // ห้อง Feedback ลูกค้าภายนอก
                  }
                break;
            }
            console.log(groupid);
            

        //แจ้งเตือนไปยังห้อง Mapp
        const newRequest = new Request(url, {
        body: JSON.stringify({
            text: message, 
            chat_id: groupid,
            photo: '',
        }),
        headers: {
            'Content-Type': 'application/json',
        },
        method: 'POST',
        redirect: 'follow',
        })
        await fetch(newRequest);
        return true;
    } catch (error) {
        console.log("error function sendNotiTG");
        console.log(error);
        return false;
    }
};

export { sendNotiTG };