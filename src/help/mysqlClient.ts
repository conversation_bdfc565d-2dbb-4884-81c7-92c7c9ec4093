import { Client } from "../driver/mysql";
import * as CryptoJ<PERSON> from "crypto-js";

export class MysqlClient {
  private options: any;

  constructor(options?: any) {
    this.options = options;
  }

  public async connect(
    database: string,
    query: string,
    param: any,
    table?: string,
    condition?: string
  ): Promise<any> {
    try {
      const mysql = new Client();
      var hostname = TUNNEL_HOST;
      var client_id = CF_CLIENT_ID;
      var client_secret = CF_CLIENT_SECRET;

      // console.log("API ENV : " + ENV_API);
      //เซ็ท connect to RDS UAT ถ้ารันบน API uat
      if (ENV_API == "UAT") {
        hostname = TUNNEL_HOST_UAT;
        client_id = CF_CLIENT_ID_UAT;
        client_secret = CF_CLIENT_SECRET_UAT;
      }
      const mysqlClient = await mysql.connect({
        username: DB_USER,
        db: database,
        hostname: hostname || "https://tunnelaws-uat.agilesoftgroup.com/",
        password: DB_PASS,
        cfClientId: client_id || undefined,
        cfClientSecret: client_secret || undefined,
      });

      const encryptedData = [];
      if (this.options) {
        // ฟังก์ชันเข้ารหัสข้อมูลด้วย Crypto.js
        var key = CryptoJS.enc.Hex.parse(this.options.secret);
        var iv = CryptoJS.enc.Hex.parse(this.options.secret.substring(0, 16));
        function encryptData(data, secret) {
          if (typeof data === "string") {
            const ciphertext = CryptoJS.AES.encrypt(data, key, {
              iv: iv,
            }).toString();
            return ciphertext;
          }
          return data;
        }

        function decryptData(data, secret) {
          const isBase64 = (str) => {
            try {
              return btoa(atob(str)) == str;
            } catch (err) {
              return false;
            }
          };
          if (typeof data === "string" && isBase64(data)) {
            const bytes = CryptoJS.AES.decrypt(data, key, {
              iv: iv,
            });
            const originalText = bytes.toString(CryptoJS.enc.Utf8);
            return originalText;
          }
          return data;
        }

        function generateInsertQuery(data, table) {
          const fields = Object.keys(data);
          const values = Object.values(data);
          const placeholders = values.map(() => "?");

          const query = `INSERT INTO ${table} (${fields.join(
            ", "
          )}) VALUES (${placeholders.join(", ")})`;

          return query;
        }

        function generateUpdateQuery(table, data, condition) {
          const fields = Object.keys(data);
          const placeholders = fields.map((field) => `${field} = ?`);

          const query = `UPDATE ${table} SET ${placeholders.join(
            ", "
          )} WHERE ${condition}`;

          return query;
        }

        function generateParams(data) {
          return Object.values(data);
        }

        if (query.includes("SELECT") && table) {
          if (this.options.enccondition && param) {
            param = encryptData(param[0], this.options.secret);
            param = [param];
          }

          const results = await mysqlClient.query(query, param);
          // ตรวจสอบเงื่อนไขและเข้ารหัสข้อมูล
          // console.log(results);
          results.forEach((row) => {
            this.options.entable.forEach((entable) => {
              if (entable.table === table) {
                entable.fields.forEach((field) => {
                  // console.log(row);
                  if (row.hasOwnProperty(field)) {
                    row[field] = decryptData(row[field], this.options.secret);
                  }
                });
              }
            });
            encryptedData.push(row);
          });
          return encryptedData;
        } else if (query.includes("INSERT") && table && Object.keys(param)) {
          // เข้ารหัสฟิลด์ Device_name ก่อนที่จะแทรกข้อมูลลงในฐานข้อมูล
          this.options.entable.forEach((entable) => {
            if (entable.table === table) {
              entable.fields.forEach((field) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = encryptData(param[field], this.options.secret);
                }
              });
            }
          });

          const query = generateInsertQuery(param, table);
          var params = generateParams(param);
          // console.log(query);
          // console.log(params);

          const result = await mysqlClient.query(query, params);
          return result;
        } else if (query.includes("UPDATE") && table && condition) {
          this.options.entable.forEach((entable) => {
            if (entable.table === table) {
              entable.fields.forEach((field) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = encryptData(param[field], this.options.secret);
                }
              });
            }
          });
          const query = generateUpdateQuery(table, param, condition);
          const params = generateParams(param);

          // console.log(query);
          // console.log(params);

          const result = await mysqlClient.query(query, params);
          return result;
        }
      } else {
        const result = await mysqlClient.query(query, param);
        return result;
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

export class MysqlClient_V2 {
  private options: any;

  constructor(options?: any) {
    this.options = options;
  }

  private encryptData(data: string): string {
    const key = CryptoJS.enc.Hex.parse(this.options.secret);
    const iv = CryptoJS.enc.Hex.parse(this.options.secret.substring(0, 16));
    const ciphertext = CryptoJS.AES.encrypt(data, key, { iv: iv }).toString();
    return ciphertext;
  }

  private decryptData(data: string): string {
    const key = CryptoJS.enc.Hex.parse(this.options.secret);
    const iv = CryptoJS.enc.Hex.parse(this.options.secret.substring(0, 16));

    if (typeof data === "string" && this.isBase64(data)) {
      const bytes = CryptoJS.AES.decrypt(data, key, { iv: iv });
      return bytes.toString(CryptoJS.enc.Utf8);
    }
    return data;
  }

  private isBase64(str: string): boolean {
    try {
      return btoa(atob(str)) === str;
    } catch (err) {
      return false;
    }
  }

  private generateSelectQuery(query: string, param: any): string {
    // ปรับการสร้าง query SELECT ตามความต้องการ
    return query; // ในกรณีนี้ไม่ต้องปรับ query
  }

  private generateInsertQuery(data: any, table: string): string {
    const fields = Object.keys(data);
    const placeholders = fields.map(() => "?").join(", ");
    return `INSERT INTO ${table} (${fields.join(
      ", "
    )}) VALUES (${placeholders})`;
  }

  private generateUpdateQuery(
    table: string,
    data: any,
    condition: string
  ): string {
    const placeholders = Object.keys(data)
      .map((field) => `${field} = ?`)
      .join(", ");
    return `UPDATE ${table} SET ${placeholders} WHERE ${condition}`;
  }

  private processParams(data: any): any[] {
    return Object.values(data);
  }

  private processEncryption(data: any, table: string): void {
    if (this.options && this.options.entable) {
      const encryptionConfig = this.options.entable.find(
        (entable: any) => entable.table === table
      );
      if (encryptionConfig) {
        encryptionConfig.fields.forEach((field: string) => {
          if (data.hasOwnProperty(field)) {
            data[field] = this.encryptData(data[field]);
          }
        });
      }
    }
  }

  private processDecryption(row: any, table: string): void {
    if (this.options && this.options.entable) {
      const decryptionConfig = this.options.entable.find(
        (entable: any) => entable.table === table
      );
      if (decryptionConfig) {
        decryptionConfig.fields.forEach((field: string) => {
          if (row.hasOwnProperty(field)) {
            row[field] = this.decryptData(row[field]);
          }
        });
      }
    }
  }

  public async connect(
    database: string,
    query: string,
    param: any,
    table?: string,
    condition?: string
  ): Promise<any> {
    /* ######## set time out Connect Database ####### */
    // return new Promise<any>(async (resolve, reject) => {
    //   const timeout = setTimeout(() => {
    //     reject({ status: 500, message: "MySQL query timed out" });
    //   }, 20000); // 5000ms = 5 seconds
    try {
      // console.log("######");

      // console.log(condition);

      const mysql = new Client();
      var hostname = TUNNEL_HOST;
      var client_id = CF_CLIENT_ID;
      var client_secret = CF_CLIENT_SECRET;

      // console.log("API ENV : " + ENV_API);
      //เซ็ท connect to RDS UAT ถ้ารันบน API uat
      if (ENV_API == "UAT") {
        hostname = TUNNEL_HOST_UAT;
        client_id = CF_CLIENT_ID_UAT;
        client_secret = CF_CLIENT_SECRET_UAT;
      }
      const mysqlClient = await mysql.connect({
        username: DB_USER,
        db: database,
        hostname: hostname || "https://tunnelaws-uat.agilesoftgroup.com/",
        password: DB_PASS,
        cfClientId: client_id || undefined,
        cfClientSecret: client_secret || undefined,
      });

      const encryptedData = [];
      if (this.options) {
        if (query.includes("SELECT") && table) {
          if (this.options.enccondition && param) {
            param = this.encryptData(param[0], this.options.secret);
            param = [param];
          }
          const results = await mysqlClient.query(query, param);

          results.forEach((row) => {
            this.options.entable.forEach((entable) => {
              if (entable.table === table) {
                entable.fields.forEach((field) => {
                  // console.log(row);
                  if (row.hasOwnProperty(field)) {
                    row[field] = this.decryptData(
                      row[field],
                      this.options.secret
                    );
                  }
                });
              }
            });
            encryptedData.push(row);
          });
          return encryptedData;
        } else if (
          query.includes("INSERT") &&
          table &&
          Object.keys(param).length
        ) {
          this.processEncryption(param, table);

          const insertQuery = this.generateInsertQuery(param, table);
          const params = this.processParams(param);
          return await mysqlClient.query(insertQuery, params);
        } else if (query.includes("UPDATE") && table && condition) {
          this.processEncryption(param, table);

          const updateQuery = this.generateUpdateQuery(table, param, condition);
          const params = this.processParams(param);

          return await mysqlClient.query(updateQuery, params);
        }
      } else {
        // แยกการสร้าง query ตามประเภท
        let generatedQuery: string;
        if (query.includes("SELECT")) {
          generatedQuery = this.generateSelectQuery(query, param);
        } else if (
          query.includes("INSERT") &&
          table &&
          Object.keys(param).length
        ) {
          generatedQuery = this.generateInsertQuery(param, table);
        } else if (query.includes("UPDATE") && table && condition) {
          generatedQuery = this.generateUpdateQuery(table, param, condition);
        } else {
          generatedQuery = query; // ใช้ query เดิมสำหรับกรณีที่ไม่ครอบคลุม
        }

        const params = this.processParams(param);
        return await mysqlClient.query(generatedQuery, params);
      }
    } catch (error) {
      console.log("connect MysqlClient has catch error please check !!!!");
      console.log(error);
      throw error;
    }
    // });
  }
}

// Connect SQL byPass ไม่เข้ากระบวนการ encrypt
export class MysqlClientWithOutParams {
  private options: any;

  constructor(options?: any) {
    this.options = options;
  }

  public async connect(database: string, query: string): Promise<any> {
    try {
      const mysql = new Client();
      var hostname = TUNNEL_HOST;
      var client_id = CF_CLIENT_ID;
      var client_secret = CF_CLIENT_SECRET;

      // console.log("API ENV : " + ENV_API);
      //เซ็ท connect to RDS UAT ถ้ารันบน API uat
      if (ENV_API == "UAT") {
        hostname = TUNNEL_HOST_UAT;
        client_id = CF_CLIENT_ID_UAT;
        client_secret = CF_CLIENT_SECRET_UAT;
      }
      const mysqlClient = await mysql.connect({
        username: DB_USER,
        db: database,
        hostname: hostname || "https://tunnelaws-uat.agilesoftgroup.com/",
        password: DB_PASS,
        cfClientId: client_id || undefined,
        cfClientSecret: client_secret || undefined,
      });
      const result = await mysqlClient.query(query, []);
      return result;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

// ######## version เก่า AAM
export class MysqlClient_old {
  private options: any;

  constructor(options?: any) {
    this.options = options;
  }

  public async connect(
    database: string,
    query: string,
    param: any,
    table?: string,
    condition?: string
  ): Promise<any> {
    try {
      const mysql = new Client();
      var hostname = TUNNEL_HOST;
      var client_id = CF_CLIENT_ID;
      var client_secret = CF_CLIENT_SECRET;

      // console.log("API ENV : " + ENV_API);
      //เซ็ท connect to RDS UAT ถ้ารันบน API uat
      if (ENV_API == "UAT") {
        hostname = TUNNEL_HOST_UAT;
        client_id = CF_CLIENT_ID_UAT;
        client_secret = CF_CLIENT_SECRET_UAT;
      }
      const mysqlClient = await mysql.connect({
        username: DB_USER,
        db: database,
        hostname: hostname || "https://tunnelaws-uat.agilesoftgroup.com/",
        password: DB_PASS,
        cfClientId: client_id || undefined,
        cfClientSecret: client_secret || undefined,
      });

      var key = CryptoJS.enc.Hex.parse(this.options.secret);
      var iv = CryptoJS.enc.Hex.parse(this.options.secret.substring(0, 16));

      const encryptedData = [];
      if (this.options) {
        // ฟังก์ชันเข้ารหัสข้อมูลด้วย Crypto.js
        function encryptData(data, secret) {
          if (typeof data === "string") {
            const ciphertext = CryptoJS.AES.encrypt(data, key, {
              iv: iv,
            }).toString();
            return ciphertext;
          }
          return data;
        }

        function decryptData(data, secret) {
          const isBase64 = (str) => {
            try {
              return btoa(atob(str)) == str;
            } catch (err) {
              return false;
            }
          };
          if (typeof data === "string" && isBase64(data)) {
            const bytes = CryptoJS.AES.decrypt(data, key, {
              iv: iv,
            });
            const originalText = bytes.toString(CryptoJS.enc.Utf8);
            return originalText;
          }
          return data;
        }

        function generateInsertQuery(data, table) {
          const fields = Object.keys(data);
          const values = Object.values(data);
          const placeholders = values.map(() => "?");

          const query = `INSERT INTO ${table} (${fields.join(
            ", "
          )}) VALUES (${placeholders.join(", ")})`;

          return query;
        }

        function generateUpdateQuery(table, data, condition) {
          const fields = Object.keys(data);
          const placeholders = fields.map((field) => `${field} = ?`);

          const query = `UPDATE ${table} SET ${placeholders.join(
            ", "
          )} WHERE ${condition}`;

          return query;
        }

        function generateParams(data) {
          return Object.values(data);
        }

        if (query.includes("SELECT") && table) {
          if (this.options.enccondition && param) {
            param = encryptData(param[0], this.options.secret);
            param = [param];
          }

          // console.log("######");

          // console.log(query);
          // console.log(param);

          const results = await mysqlClient.query(query, param);
          // ตรวจสอบเงื่อนไขและเข้ารหัสข้อมูล
          // console.log(results);
          results.forEach((row) => {
            // console.log("### row ##");
            // console.log(row);

            this.options.entable.forEach((entable) => {
              // console.log("%%% entable %%%");
              // console.log(entable);

              if (entable.table === table) {
                entable.fields.forEach((field) => {
                  // console.log("###### field #####");
                  // console.log(field);

                  // console.log(row);
                  if (row.hasOwnProperty(field)) {
                    // console.log("############");
                    // console.log(row[field]);

                    row[field] = decryptData(row[field], this.options.secret);
                  }
                });
              }
            });
            encryptedData.push(row);
          });
          return encryptedData;
        } else if (query.includes("INSERT") && table && Object.keys(param)) {
          // เข้ารหัสฟิลด์ Device_name ก่อนที่จะแทรกข้อมูลลงในฐานข้อมูล
          this.options.entable.forEach((entable) => {
            if (entable.table === table) {
              entable.fields.forEach((field) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = encryptData(param[field], this.options.secret);
                }
              });
            }
          });

          const query = generateInsertQuery(param, table);
          var params = generateParams(param);
          // console.log(query);
          // console.log(params);

          const result = await mysqlClient.query(query, params);
          return result;
        } else if (query.includes("UPDATE") && table && condition) {
          this.options.entable.forEach((entable) => {
            if (entable.table === table) {
              entable.fields.forEach((field) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = encryptData(param[field], this.options.secret);
                }
              });
            }
          });
          const query = generateUpdateQuery(table, param, condition);
          const params = generateParams(param);

          // console.log(query);
          // console.log(params);

          const result = await mysqlClient.query(query, params);
          return result;
        }
      } else {
        const result = await mysqlClient.query(query, param);
        return result;
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

export class MysqlClientHandler {
  private options: any;

  constructor(options?: any) {
    this.options = options;
  }

  private encryptData(data: string, secret: string): string {
    const key = CryptoJS.enc.Hex.parse(secret);
    const iv = CryptoJS.enc.Hex.parse(secret.substring(0, 16));
    if (typeof data === "string") {
      const ciphertext = CryptoJS.AES.encrypt(data, key, { iv }).toString();
      return ciphertext;
    }
    return data;
  }

  // private decryptData(data: string, secret: string): string {
  //   const key = CryptoJS.enc.Hex.parse(secret);
  //   const iv = CryptoJS.enc.Hex.parse(secret.substring(0, 16));

  //   const isBase64 = (str: string) => {
  //     try {
  //       return btoa(atob(str)) === str;
  //     } catch (err) {
  //       return false;
  //     }
  //   };

  //   if (typeof data === "string" && isBase64(data)) {
  //     const bytes = CryptoJS.AES.decrypt(data, key, { iv });
  //     const originalText = bytes.toString(CryptoJS.enc.Utf8);
  //     return originalText;
  //   }
  //   return data;
  // }

  private decryptData(data: string, secret: string): string {
    if (!data) {
      throw new Error("Data is empty");
    }

    const key = CryptoJS.enc.Hex.parse(secret);
    const iv = CryptoJS.enc.Hex.parse(secret.substring(0, 16));

    const isBase64 = (str: string) => {
      try {
        return btoa(atob(str)) === str;
      } catch (err) {
        return false;
      }
    };

    if (typeof data === "string" && isBase64(data)) {
      try {
        const bytes = CryptoJS.AES.decrypt(data, key, { iv });
        const originalText = bytes.toString(CryptoJS.enc.Utf8);

        // ตรวจสอบว่า originalText เป็นค่าว่างหรือไม่
        if (!originalText) {
          // throw new Error("Decryption resulted in empty or invalid data");
          return "";
        }
        return originalText;
      } catch (err) {
        // จัดการข้อผิดพลาด Malformed UTF-8 data
        if (err.message.includes("Malformed UTF-8 data")) {
          console.error(
            "Failed to decode decrypted data as UTF-8:",
            err.message
          );
          // throw new Error("Decrypted data is not valid UTF-8");
        } else {
          console.error("Failed to decrypt data:", err.message);
          // throw new Error("Failed to decrypt data: " + err.message);
        }
        return "";
      }
    } else {
      // throw new Error("Data is not a valid Base64 string");
      return "";
    }
  }

  private decryptData_(data: string, secret: string): string {
    if (!data) return ""; // ป้องกัน data เป็นค่าว่าง

    const key = CryptoJS.enc.Hex.parse(secret);
    const iv = CryptoJS.enc.Hex.parse(secret.substring(0, 16));

    const isBase64 = (str: string) => {
      try {
        return btoa(atob(str)) === str;
      } catch (err) {
        return false;
      }
    };

    if (typeof data === "string" && isBase64(data)) {
      try {
        const bytes = CryptoJS.AES.decrypt(data, key, { iv });

        // ตรวจสอบว่า bytes ไม่ว่างและเป็นข้อมูลที่ถูกต้อง
        if (!bytes || bytes.sigBytes <= 0) {
          console.warn("Decryption failed: Empty bytes");
          return "";
        }

        const originalText = bytes.toString(CryptoJS.enc.Utf8);

        // ตรวจสอบผลลัพธ์หลังแปลงเป็น UTF-8
        if (!originalText) {
          console.warn("Decryption failed: Malformed UTF-8 data");
          return "";
        }

        return originalText;
      } catch (error) {
        console.error("Decryption error:", error);
        return "";
      }
    }

    return data; // คืนค่าข้อมูลเดิมหากไม่ใช่ Base64
  }

  private generateInsertQuery(data: any, table: string): string {
    const fields = Object.keys(data);
    const placeholders = fields.map(() => "?").join(", ");
    return `INSERT INTO ${table} (${fields.join(
      ", "
    )}) VALUES (${placeholders})`;
  }

  private generateUpdateQuery(
    table: string,
    data: any,
    condition: string
  ): string {
    const fields = Object.keys(data).map((field) => `${field} = ?`);
    return `UPDATE ${table} SET ${fields.join(", ")} WHERE ${condition}`;
  }

  private generateParams(data: any): any[] {
    return Object.values(data);
  }

  public async connect(
    database: string,
    query: string,
    param: any[],
    table?: string,
    condition?: string // if true ต้องเข้ารหัส(encrypt) params ก่อนส่งไป query
  ): Promise<any> {
    try {
      // console.log(database);
      // console.log(query);
      // console.log(param);
      // console.log(table);
      // console.log(condition);

      const mysql = new Client();
      var hostname = TUNNEL_HOST;
      var client_id = CF_CLIENT_ID;
      var client_secret = CF_CLIENT_SECRET;

      // console.log("API ENV : " + ENV_API);
      //เซ็ท connect to RDS UAT ถ้ารันบน API uat
      if (ENV_API == "UAT") {
        hostname = TUNNEL_HOST_UAT;
        client_id = CF_CLIENT_ID_UAT;
        client_secret = CF_CLIENT_SECRET_UAT;
      }

      // console.log("hostname : " + hostname);
      // console.log("client_id :" + client_id);
      // console.log("client_secret : " + client_secret);
      const mysqlClient = await mysql.connect({
        username: DB_USER,
        db: database,
        hostname: hostname || "https://tunnelaws-uat.agilesoftgroup.com/",
        password: DB_PASS,
        cfClientId: client_id || undefined,
        cfClientSecret: client_secret || undefined,
      });

      if (this.options) {
        if (query.includes("SELECT") && table) {
          // เข้ารหัสพารามิเตอร์ในเงื่อนไข WHERE ( เข้ารหัส params ก่อนส่งเข้าไป query ถ้า this.options.enccondition == true encrypt param ทั้งหมด เช่น ส่งเบอร์โทรไปค้นหาข้อมูล)
          if (this.options.enccondition && param) {
            param = param.map((p) =>
              typeof p === "string"
                ? this.encryptData(p, this.options.secret)
                : p
            );
          }
          // else {
          //   console.log("else มาที่นี่ ไม่ต้องเข้ารหัส params ก่อน query");
          // }

          // query RDS DB
          const results = await mysqlClient.query(query, param);

          // console.log("result จาก https://tunnelaws-uat.agilesoftgroup.com");
          // console.log(results.length);

          const tableConfig = this.options.entable.find(
            (entable: any) => entable.table === table
          );

          // ถ้าระบุ fields ที่ต้องการถอดรหัสมา ให้เข้ากระบวนการ decryptData
          if (tableConfig.fields.length > 0) {
            // ถอดรหัสข้อมูลในผลลัพธ์
            const decryptedData = results.map((row: any) => {
              // console.log("เข้า decryptedData");
              // console.log("tableConfig");
              // console.log(tableConfig.fields);

              if (tableConfig) {
                tableConfig.fields.forEach((field: string) => {
                  try {
                    if (row.hasOwnProperty(field)) {
                      row[field] = this.decryptData(
                        row[field],
                        this.options.secret
                      );
                    }
                  } catch (error) {
                    console.log("decryptData error: " + error);
                  }
                });
              }
              return row;
            });

            return decryptedData;
          } else {
            return results;
          }
        } else if (
          query.includes("INSERT") &&
          table &&
          Object.keys(param).length
        ) {
          // เข้ารหัสฟิลด์ใน param ก่อนแทรกข้อมูล
          this.options.entable.forEach((entable: any) => {
            if (entable.table === table) {
              entable.fields.forEach((field: string) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = this.encryptData(
                    param[field],
                    this.options.secret
                  );
                }
              });
            }
          });

          const insertQuery = this.generateInsertQuery(param, table);
          const params = this.generateParams(param);

          const result = await mysqlClient.query(insertQuery, params);
          return result;
        } else if (query.includes("UPDATE") && table && condition) {
          // เข้ารหัสฟิลด์ใน param ก่อนอัปเดตข้อมูล
          this.options.entable.forEach((entable: any) => {
            if (entable.table === table) {
              entable.fields.forEach((field: string) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = this.encryptData(
                    param[field],
                    this.options.secret
                  );
                }
              });
            }
          });

          const updateQuery = this.generateUpdateQuery(table, param, condition);
          const params = this.generateParams(param);

          const result = await mysqlClient.query(updateQuery, params);
          return result;
        }
      } else {
        // สำหรับ query ที่ไม่ต้องการการเข้ารหัส/ถอดรหัส
        const result = await mysqlClient.query(query, param);
        return result;
      }
    } catch (error) {
      console.error("Database operation failed:", error);
      throw error;
    }
  }
}