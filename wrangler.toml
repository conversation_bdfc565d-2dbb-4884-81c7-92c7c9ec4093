name = "api_aamg_aam"
main = "./dist/index.mjs"
compatibility_date = "2024-03-11"
node_compat = true
zone_id = '6f8fd5e414b56f90500b00460a11cebf'
account_id = '71db72daf6438fb1c04a3c4fbceff9ae'

[build]
command = "npm install && npm run build && cp ./src/driver/**/*.wasm ./dist" #<--- mysql require

[vars]
ENVIRONMENT = 'dev'
ENV_VERSION = 'UAT' # UAT = ถ้าใช้สำหรับเวอร์ัน UAT , PROD ถ้าใช้งานบนเวอร์ชัน production  
BUNAME = 'aam' #<----- เปลี่ยนชื่อ bu ตรงนี้ก่อน deploy. ของ staging และ product ก็ต้องเปลี่ยนด้วย. ถ้า test บน local แก้แค่นี้พอ
DB_USER = "webapp_admin"
DB_PASS = "9N61KigjbJ2llK155G"
aamDB = "applicationAAM" #<-- เส้น UAT ใช้ applicationAAM_dev เส้นจริงใช้ applicationAAM
aamDB_UAT = "applicationAAM_dev"
rplcDB = "webRPLC"
rafcoDB = "MApp_Rafco"
DB_AMS4 = "ams4"
DB_AMS2 = "BCT_AMS2"
DB_AMS2_RAFCO = "BCT_AMS2_RAFCO"
DB_AMS4_RPLC = "ams4_RPLC"
DB_AMS4_RAFCO = "ams4_RAFCO"
DB_AMS2_RPLC = "BCT_AMS2_RPLC"
DB_AMS3_THAI = "bct_ams3"
DB_AMS3_RPLC = "bct_ams3_rplc"
DB_AMS3_RAFCO = "bct_ams3_rafco"
DB_BCT_Datacenter = "BCT_customer_datacenter"
DB_BCT_Datacenter_RPLC = "BCT_customer_datacenter_RPLC"
DB_BCT_Datacenter_RAFCO = "BCT_customer_datacenter_RAFCO"
GET_TOKEN = "https://y38cw14k1b.execute-api.ap-southeast-1.amazonaws.com/prod/auth/gettoken/v1"
AUTH_TOKEN = "ZtMOSYbSZQ4T0p0e1XNOU4DenMNpq80s6GdlTnPt"
HCS_URL = "https://68wtlqk0s2.execute-api.ap-southeast-1.amazonaws.com/dev/submitMessageTopic"
HCS_X_API = "sCzIzOuTio8LOr9OigCJj4nfuwxWudmQ4WCvyfiP"
HCS_TOPIC_ID = "0.0.1310614"
Lambda_X_API = "JNjDm8nvnPvzIS9U45Uf8lCrz6D5GaT8c5Rq0Nh7"
LP2_PAY_URL = "https://api2.likepoint.io/transactions-activity/pay-poi-in-app"
LP2_X_API = "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu"
TUNNEL_HOST_UAT = "https://tunnelaws-uat.agilesoftgroup.com" #-- connect RDS UAT
CF_CLIENT_ID_UAT = "713de51f3fb2c23ebf0bc9652dd8b939.access" #-- connect RDS UAT
CF_CLIENT_SECRET_UAT = "e04275f112ee47985f655b333266c26ec84d932be534d659cb6ec70552c046e2" #-- connect RDS UAT
TUNNEL_HOST = "https://tunnelaws.agilesoftgroup.com"
CF_CLIENT_ID = "0bf2bd876ed819d54fc49d11dfb709d6.access"
CF_CLIENT_SECRET = "8bea9fdd917637cd91aad6472de5df9cc0f6f39a67bb46839015e6d095b3d452"
DB_SECRET = "Hz4PzDNFEfAE9L7jGcBKpBXU"
jwtid = "uuidv4()"
MY_SQL_CLIENT_CON = "https://agilesoftgroup.com/mysql/query2"
REALM_APPID = "cloudflare-worker-airlf"
AUTH_REALM_KEY = "ZnjYVU4XvujlflJT4hmOUmN63rSjKzfQjNmtJJUZ6lbB9dq7uWvECXvCqz1FQ0ab"
GET_BALANCELIKE = "https://new.likepoint.io/getBalanceByphoneNumber"
OCR_API_KEY = "WbSfyRnGJWlPsgXNYZuXhgxewAwGwCMu"
OCR_API_URL = "https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest/readApiByType"
AAM_BOOKBANK_URL = "https://us-central1-mappaam-44857.cloudfunctions.net/getdetailbookbank"
REFERAL_URL = "https://api2.likepoint.io/member/get-ref-code/"
REF_X_API = "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu"
CLIENT_EMAIL = "<EMAIL>"
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GOOGLE_OAUTH2_URL = "https://oauth2.googleapis.com/token"
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
CLIENT_EMAIL_RPLC = "<EMAIL>"
CLIENT_EMAIL_RAFCO = "<EMAIL>"
FCM_URL_AAM = "https://fcm.googleapis.com/v1/projects/mappaam-44857/messages:send"
FCM_URL_RPLC = "https://fcm.googleapis.com/v1/projects/mapprplc/messages:send"
FCM_URL_RAFCO = "https://fcm.googleapis.com/v1/projects/mapprafco/messages:send"
QR_CODE_PAY_URL = "https://byp1dympee.execute-api.ap-southeast-1.amazonaws.com/latest/qrcode_pay_aamapp"
N8N_OCR_URL = "https://n8n.agilesoftgroup.com/webhook/bd15e000-da97-4fc1-99cd-9ee859f578f6"
KYC_BANK_URL = "https://kyc-bookbank.agilesoftgroup.com/api/action"
FIRESTORE_BOOKBANK_CREATE = "https://createbookbanktofirestorenew-om5dix5gfa-uc.a.run.app"
FIRESTORE_BOOKBANK_CREATE_RPLC = "https://ukxppovr06.execute-api.ap-southeast-1.amazonaws.com/latest/RPLC/saveBookbank"
KEY_LAMBDA_RPLC = "cRrMpCgqAb7FbzIVF9jfB9xNoWZsPL1O9isIJt7R"
KYC_LAO = "http://kyc-laos.agilesoftgroup.com/api/KYC_LAO?sessionId=test-session&acc_num="
LAMBDA_RPLC_URL = "https://ukxppovr06.execute-api.ap-southeast-1.amazonaws.com/latest/RPLC"
AMS4_API_TH = "https://ams4.prachakij.com/api/v1/"
AMS4_API_LAO = "https://ams4rplc.prachakij.com/api/v1/"
AMS4_API_RAFCO = "https://ams3rafco.prachakij.com/api/v1/"
MAPP_CENTER = "https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest"
MAPP_CENTER_API_KEY = "FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky"


#------------------------------- CRON JOB scheduled ---------------------------------
[triggers]
crons = ["30 21 * * *"] # set time zone +7 (ทำงานเฉพาะ on production)

#------------------------------- BEWARE!!! Sinc this line below is neccesary on deploy production ---------------------------------

[env.staging]
name = "api_aamg_aam-staging" #<-- เมื่อ merge เข้าหากัน เปลี่ยนชื่อ api_aamg_...-staging ตาม BUNAME   , เช็ค BUNAME
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
route = "aam-staging.prachakij.workers.dev/AAMGp3-UAT/*"  #<-- เมื่อ merge เปลี่ยนชื่อ Endpoint -> RAFCOp3, RPLCp3, AAMGp3
services = [
  { binding = "SQLBIND", service = "cf-mysql-staging", environment = "production" }
]

[env.production]
## name = "-production" , name = "-uat"
name = "api_aamg_aam-uat" #<-- เมื่อ merge เข้าหากัน เปลี่ยนชื่อ api_aamg_...-uat ตาม BUNAME   , เช็ค BUNAME 
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
route = "agilesoftgroup.com/AAMGp3-UAT/*"  #<-- เมื่อ merge เปลี่ยนชื่อ Endpoint -> RAFCOp3, RPLCp3, AAMGp3
services = [
  { binding = "SQLBIND", service = "cf-mysql-staging", environment = "production" }
]
logpush = true