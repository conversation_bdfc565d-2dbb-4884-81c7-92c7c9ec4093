{"name": "api_aamg_center", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "esbuild --bundle --sourcemap --outfile=dist/index.mjs --minify --format=esm ./src/index.js --external:*.wasm --inject:./src/deno/workers-override.ts", "dev": "wrangler dev", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@agilesoft/type_ags_authrest2": "^1.1.8", "@cloudflare/wrangler": "^1.21.0", "@types/crypto-js": "^4.1.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns-tz": "^3.1.3", "esbuild": "^0.18.16", "itty-router": "^4.0.27", "joi": "^17.12.2", "luxon": "^3.4.4", "moment-timezone": "^0.5.48", "realm-web": "^2.0.0", "string-strip-html": "^13.4.2", "wrangler": "^3.78.2"}, "devDependencies": {"wrangler": "^3.78.2"}}