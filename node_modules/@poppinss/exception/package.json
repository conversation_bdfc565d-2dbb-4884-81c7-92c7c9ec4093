{"name": "@poppinss/exception", "description": "Utility to create custom exceptions", "version": "1.2.2", "type": "module", "files": ["build", "!build/bin", "!build/tests"], "main": "build/index.js", "exports": {".": "./build/index.js"}, "scripts": {"pretest": "npm run lint", "test": "c8 npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "precompile": "npm run lint", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "build": "npm run compile", "version": "npm run build", "prepublishOnly": "npm run build", "release": "release-it", "quick:test": "node --import=@poppinss/ts-exec --enable-source-maps bin/test.ts"}, "devDependencies": {"@adonisjs/eslint-config": "^3.0.0-next.0", "@adonisjs/prettier-config": "^1.4.5", "@adonisjs/tsconfig": "^2.0.0-next.0", "@japa/expect": "^3.0.4", "@japa/expect-type": "^2.0.3", "@japa/runner": "^4.2.0", "@poppinss/ts-exec": "^1.4.0", "@release-it/conventional-changelog": "^10.0.1", "@types/node": "^24.0.10", "c8": "^10.1.3", "eslint": "^9.30.1", "prettier": "^3.6.2", "release-it": "^19.0.3", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "homepage": "https://github.com/poppinss/exception#readme", "repository": {"type": "git", "url": "git+https://github.com/poppinss/exception.git"}, "bugs": {"url": "https://github.com/poppinss/exception/issues"}, "keywords": [], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "tsup": {"entry": ["index.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**"]}, "prettier": "@adonisjs/prettier-config"}