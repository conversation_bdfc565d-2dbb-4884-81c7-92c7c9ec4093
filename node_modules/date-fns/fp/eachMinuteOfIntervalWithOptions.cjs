"use strict";
exports.eachMinuteOfIntervalWithOptions = void 0;

var _index = require("../eachMinuteOfInterval.cjs");
var _index2 = require("./_lib/convertToFP.cjs"); // This file is generated automatically by `scripts/build/fp.ts`. Please, don't change it.

const eachMinuteOfIntervalWithOptions =
  (exports.eachMinuteOfIntervalWithOptions = (0, _index2.convertToFP)(
    _index.eachMinuteOfInterval,
    2,
  ));
