{"name": "@cloudflare/binary-install", "version": "0.2.0", "description": "Install binary applications via npm", "main": "./index.js", "scripts": {"fmt": "prettier --write **/*.js", "fmt:check": "prettier --check **/*.js"}, "engines": {"node": ">=10"}, "repository": {"type": "git", "url": "git+https://github.com/cloudflare/binary-install.git"}, "keywords": ["install", "binary", "global"], "author": "Wrangler Team <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/cloudflare/binary-install/issues"}, "homepage": "https://github.com/cloudflare/binary-install#readme", "devDependencies": {"prettier": "^1.19.1"}, "dependencies": {"axios": "^0.21.1", "rimraf": "^3.0.2", "tar": "^6.0.2"}}